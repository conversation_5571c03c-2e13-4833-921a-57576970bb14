/* eslint-disable @typescript-eslint/no-explicit-any */
import { NotFound } from '@components/404'
import {
    ContentPage,
    ContentPageProps
} from '@components/common/ContentPage/ContentPage'
import { PDPProductProvider } from '@components/common/ContentPage/ContentPagePDPProductContext'
import { GalleryTypeEnum } from '@components/common/ProductGallery/ProductGallery'
import { PSDStateContext } from '@components/common/PSDStateProvider/PSDStateContext'
import type { TypeComponentsTab } from '@components/common/Tabs/ProductTabContent'
import type { IProductContentfulResponse } from '@components/common/types'
import { useLayoutContext } from '@components/layouts/MainLayout/LayoutContext'
import { MainLayout } from '@components/layouts/MainLayout/MainLayout'
import { PRODUCT_VIEW_AR_ID } from '@components/molecules/ViewArBanner/ViewArBanner'
import { ProductConfigurator } from '@components/templates/AboveTheFold/ProductConfigurator'
import {
    SocialDefaultContent,
    socialDefaultContent
} from '@config/seo/defaultContents'
import { SEO } from '@corsairitshopify/corsair-seo'
import { pushToDataLayer } from '@corsairitshopify/pylot-gtm/src/utils/pushToDataLayer'
import { useContentGroup } from '@corsairitshopify/pylot-tag-manager/src/TrackContentGroup'
import { Container } from '@corsairitshopify/pylot-ui'
import {
    getYotpoBottomLineApi,
    yotpoJsonLd,
    YotpoScripts
} from '@corsairitshopify/pylot-yotpo'
import { getSharedLayoutProps } from '@lib/getSharedLayoutProps'
import { getPdpEvent, useAdditionContent } from '@lib/gtm/pdp'
import s from '@pagestyles/PDP.module.scss'
import { getBazaarvoiceSummary } from '@pylot-data/api/operations/get-bazaarvoice'
import getContentJson from '@pylot-data/api/operations/get-content-json'
import getProduct from '@pylot-data/api/operations/get-product'
import { getRelatedProducts } from '@pylot-data/api/operations/get-related-products'
import { ConfigurableVariant, ProductInterface } from '@pylot-data/fwrdschema'
import {
    ContentJsonItem,
    useContentJson
} from '@pylot-data/hooks/contentful/use-content-json'
import { useProductFetch } from '@pylot-data/hooks/product/use-product-fetch'
import {
    ProductVariant,
    useProductUI
} from '@pylot-data/hooks/product/use-product-ui'
import { getMediaGalleryEntries } from '@pylot-data/hooks/product/utils/getMediaGalleryEntries'
import { serverSideTranslations } from '@pylot-data/serverSideTranslations'
import cn from 'classnames'
import { getStoreConfig, useStoreConfig } from 'config'
import { convertImageFormat } from 'helpers/cloudinaryHelper'
import { filterElgBDSProducts } from 'helpers/elgatoBDSHelper'
import type {
    GetStaticPaths,
    GetStaticProps,
    GetStaticPropsContext,
    InferGetStaticPropsType
} from 'next'
import { useTranslation } from 'next-i18next'
import dynamic from 'next/dynamic'
import Head from 'next/head'
import { useRouter } from 'next/router'
import Script from 'next/script'
import React, {
    ReactElement,
    useContext,
    useEffect,
    useMemo,
    useRef
} from 'react'

const ProductGallery = dynamic(
    () => import('@components/common/ProductGallery')
)

const BazaarLoader = dynamic(
    () => import('@components/molecules/BazaarComponents/BazaarLoader'),
    { ssr: false }
)
const rel = 'alternate'
const PRODUCT_CONTENT_TYPE = 'product'

function grabAllSkus(skusList: string[][]): Array<string> {
    return skusList?.flat(2)?.filter((sku) => sku) || []
}

function getProductsBySkus(
    products: ProductInterface[],
    skusList: Array<string[] | null>
): Array<ProductInterface[]> {
    return skusList.map((skus) =>
        products?.filter(
            (product) => product?.sku && skus?.includes(product?.sku)
        )
    )
}

async function getRelatedProductsData(skus: string[], locale: string) {
    const products = await getRelatedProducts(skus, locale)
    const productsData: ProductInterface[] = []

    products?.forEach((productData) => {
        const index = productData?.productData?.findIndex(
            (product, i) => product?.sku === productData?.productData?.[i]?.sku
        )

        if (index != null && productData?.productData?.[index]) {
            productsData.push(productData.productData[index])
        }
    })

    /* This will return original productsData without sorted.
    return productsData?.filter(Boolean) */

    //This will return productsData after sorted by SKU's position in skus list.
    const sortedProductsData: ProductInterface[] = []
    skus?.forEach((sku) => {
        productsData.forEach((productData) => {
            if (sku === productData?.sku) {
                sortedProductsData.push(productData)
            }
        })
    })
    return sortedProductsData?.filter(Boolean)
}

function convertProductContentfulToContentPage(
    productContentfulToContentPage: IProductContentfulResponse<TypeComponentsTab>
): Omit<ContentJsonItem<ContentPage>, 'entries'> {
    return {
        identifier: productContentfulToContentPage.identifier,
        parsedEntries: {
            identifier: productContentfulToContentPage.identifier,
            pageContentEntries: productContentfulToContentPage.contentModules as ContentPageProps,
            meta: productContentfulToContentPage.meta,
            title: '',
            useFaqSchema: false
        }
    }
}

function getUrlKey(
    url_key: string,
    { isFallback, asPath }: ReturnType<typeof useRouter>
): string {
    if (!isFallback) {
        return url_key
    }
    const routeParts = /\/([^/]+)\/(.+)/gi.exec(asPath)
    return routeParts?.length === 3 ? routeParts[2] : ''
}

export const getStaticPaths: GetStaticPaths = async () => {
    // Disabled preloading
    return {
        paths: [],
        fallback: 'blocking'
    }
}

export const getStaticProps: GetStaticProps = async ({
    locale,
    params
}: GetStaticPropsContext) => {
    // category adds .html postfix
    const productUrlWithPostfix = params!.product_url as string
    const productUrl = productUrlWithPostfix.replace('.html', '')

    // TODO: remove that
    try {
        const product = await getProduct({
            url_key: productUrl,
            locale: locale || ''
        })

        if (!product || !product?.productDetail?.items.length) {
            return {
                notFound: true,
                revalidate: 300
            }
        }
        const productSku = product?.productDetail?.items[0].sku as string

        const productDetailItem = product?.productDetail?.items[0]
        const variantIndex =
            (productDetailItem?.variants as ConfigurableVariant[])?.findIndex(
                (variant) =>
                    variant!.product!.sku?.toLowerCase() ===
                    productSku?.toLowerCase()
            ) ?? ProductVariant.NOT_SELECTED
        const mediaGalleryEntries = getMediaGalleryEntries(
            productDetailItem,
            variantIndex
        )
        const { data: productContentful } = await getContentJson<
            IProductContentfulResponse<TypeComponentsTab>
        >({
            queryVariables: {
                identifier: [productSku],
                contentType: PRODUCT_CONTENT_TYPE,
                options: {
                    queryField: 'sku',
                    level: 7
                }
            },
            locale: locale
        })

        const productContentfulWithoutEntries = {
            contentJson: productContentful?.contentJson.map((item) => {
                const { entries, ...rest } = item
                return {
                    ...rest,
                    parsedEntries: JSON.parse(entries)
                }
            })
        }
        let relatedAccessoriesSkus,
            relatedCustomerMayAlsoLikeSkus,
            relatedBundleAndSaveSkus,
            elgatoBundleAndSaveItems

        if (product) {
            const productPreFetchData = product?.productDetail?.items[0]
            if (productPreFetchData) {
                const related_accessories_skus =
                    productPreFetchData?.related_accessories_skus || []
                const customers_may_also_like_skus =
                    productPreFetchData?.customers_may_also_like_skus || []
                const bundle_and_save_skus =
                    productPreFetchData?.bundle_and_save_skus || []
                const elgato_bundle_and_save_skus =
                    productPreFetchData?.elgato_bundle_and_save_skus || []

                const skusList = [
                    related_accessories_skus,
                    customers_may_also_like_skus,
                    bundle_and_save_skus,
                    elgato_bundle_and_save_skus as string[]
                ]
                const skus = grabAllSkus(skusList)
                const products = await getRelatedProductsData(
                    skus,
                    locale || ''
                )

                ;[
                    relatedAccessoriesSkus,
                    relatedCustomerMayAlsoLikeSkus,
                    relatedBundleAndSaveSkus,
                    elgatoBundleAndSaveItems
                ] = getProductsBySkus(products, skusList)
            }

            // Fetch rating data from bazaarvoice
            const config = getStoreConfig(locale)
            const bvSummary = await getBazaarvoiceSummary(
                config,
                product.productDetail.items[0].pimId
            )
            if (Object.keys(bvSummary).length && bvSummary?.reviewCount) {
                product.productDetail.items[0] = {
                    ...product.productDetail.items?.[0],
                    review_count: bvSummary?.reviewCount || 0,
                    review_summary: bvSummary?.ratingValue,
                    best_rating: bvSummary?.bestRatingValue
                }
            }
        }
        const sharedLayoutProps = await getSharedLayoutProps(locale)
        return {
            props: {
                url_key: productUrl,
                productData: product,
                ...sharedLayoutProps,
                mediaGalleryEntries,
                productDataContentful: productContentfulWithoutEntries,
                relatedAccessoriesSkus: relatedAccessoriesSkus,
                relatedCustomerMayAlsoLikeSkus: relatedCustomerMayAlsoLikeSkus,
                relatedBundleAndSaveSkus: relatedBundleAndSaveSkus,
                elgatoBundleAndSaveItems: filterElgBDSProducts(
                    product?.productDetail?.items[0]
                        .elgato_bundle_and_save_informations,
                    product?.productDetail?.items[0].price_range,
                    elgatoBundleAndSaveItems
                ),
                ...(await serverSideTranslations(locale!, ['common', 'pdp']))
            },
            revalidate: 3600
        }
    } catch (e) {
        console.log('PDP GETSTATICPROPS ERROR')
        console.log(e)
        return {
            props: {
                url_key: productUrl,
                ...serverSideTranslations(locale!, ['common'])
            },
            revalidate: 300
        }
    }
}

export default function PDP({
    url_key,
    productData,
    productDataContentful,
    relatedAccessoriesSkus = [],
    relatedCustomerMayAlsoLikeSkus = [],
    relatedBundleAndSaveSkus = [],
    elgatoBundleAndSaveItems = [],
    mediaGalleryEntries
}: InferGetStaticPropsType<typeof getStaticProps>): ReactElement {
    const router = useRouter()
    const { t } = useTranslation(['common', 'pdp'])
    const config = getStoreConfig()
    const productMainContentRef = useRef<HTMLDivElement>(null)
    const { isViewArVisible, setPageTheme, setShowFooter } = useLayoutContext()
    const pageThemeContentful =
        productDataContentful?.contentJson?.[0]?.parsedEntries?.pageTheme
    const showFooterContentfulValue =
        productDataContentful?.contentJson?.[0]?.parsedEntries?.showFooter
    useEffect(() => {
        setPageTheme(pageThemeContentful)
        setShowFooter(showFooterContentfulValue ?? true)
    }, [
        pageThemeContentful,
        setPageTheme,
        setShowFooter,
        showFooterContentfulValue
    ])
    const { handleChangeContentGroup } = useContentGroup()!
    const { locale, asPath } = router
    const {
        base: { regionMapLanguages }
    } = useStoreConfig()
    const { setBaseLayer } = useContext(PSDStateContext)

    let path = asPath
    if (path.includes('?')) {
        path = path.substring(0, path.indexOf('?'))
    }

    if (path.endsWith('/')) {
        path = path.slice(0, -1)
    }
    const origin = config.base.url.baseUrl
    const availableCountries =
        productData?.productDetail?.items?.[0]?.available_countries || []
    const countriesLanguageAvailable = regionMapLanguages.filter(
        (countries) =>
            availableCountries?.includes(countries.region) ||
            availableCountries?.includes(countries.redirectRegion)
    )
    const [relatedAccessoriesData, setRelatedAccessoriesData] = React.useState<
        ProductInterface[]
    >([])
    const [
        relatedCustomerMayAlsoLikeData,
        setRelatedCustomerMayAlsoLikeData
    ] = React.useState<ProductInterface[]>([])
    const [
        relatedBundleAndSaveData,
        setRelatedBundleAndSaveData
    ] = React.useState<ProductInterface[]>([])
    const [elgatoBDSData, setElgatoBDSData] = React.useState<
        ProductInterface[]
    >([])
    const productSku = productData?.productDetail?.items[0].sku
    const { data } = useContentJson<
        IProductContentfulResponse<TypeComponentsTab>
    >(
        {
            identifier: [productSku],
            contentType: PRODUCT_CONTENT_TYPE,
            options: {
                queryField: 'sku',
                level: 7
            }
        },
        {
            revalidateOnFocus: false,
            revalidateOnMount: true,
            initialData: { data: productDataContentful }
        }
    )

    const productContentful = data
        ? data.find(
              (item) =>
                  item.identifier.toUpperCase() === productSku?.toUpperCase()
          )?.parsedEntries
        : null
    const {
        product,
        isSupportedProductType,
        isConfig,
        isGiftCard,
        isValidating,
        error
    } = useProductFetch(
        { productUrl: url_key },
        {
            revalidateOnFocus: false,
            revalidateOnMount: true,
            initialData: { data: productData }
        }
    )
    const productSKU = product?.sku

    useEffect(() => {
        if (productSKU) {
            handleChangeContentGroup({
                contentGroup2: 'PDP',
                contentGroup3: 'Elgato',
                contentGroup4: product?.categories[0]?.name
            })
        }

        return () => {
            handleChangeContentGroup(null)
        }
    }, [productSKU])

    useEffect(() => {
        if (productSku) {
            setBaseLayer(productSku)
        }
    }, [productSku, setBaseLayer])

    const { dataRetrived, addtionContent } = useAdditionContent()

    const contentGroup = useMemo(() => {
        return {
            contentGroup1: 'Elgato',
            contentGroup2: 'PDP',
            contentGroup3: 'Elgato',
            contentGroup4: product?.categories[0]?.name
        }
    }, [product, router.asPath])

    const review = getYotpoBottomLineApi({
        id: product?.id
    })

    const trackedUrlKey = useRef<string | null>(null)

    useEffect(() => {
        if (
            product &&
            !isValidating &&
            trackedUrlKey.current !== url_key &&
            !router.isFallback &&
            contentGroup &&
            dataRetrived
        ) {
            const event = getPdpEvent(product)
            pushToDataLayer({
                ...event,
                contentGroup,
                ...addtionContent
            })
            trackedUrlKey.current = url_key
        }
    }, [
        isValidating,
        product,
        router.isFallback,
        url_key,
        contentGroup,
        dataRetrived,
        addtionContent
    ])

    useEffect(() => {
        if (!product || !productMainContentRef.current) return
        // Redrawing any applications within the new page setup (bugfix for wrong fill urls when switching products and showing white stars)
        // see https://knowledge.bazaarvoice.com/wp-content/conversations/en_US/Display/display_integration.html#single-page-application-support
        if (typeof document !== 'undefined') {
            const ratingSummary = document.querySelector(
                '[data-bv-show="rating_summary"]'
            )
            if (ratingSummary) {
                const dataBVProductId = ratingSummary.getAttribute(
                    'data-bv-product-id'
                )
                if (dataBVProductId != null) {
                    ratingSummary.setAttribute(
                        'data-bv-product-id',
                        dataBVProductId
                    )
                }
            }
        }

        const handleSizeChange = () => {
            if (product && productMainContentRef?.current) {
                document.documentElement.style.setProperty(
                    '--productMainContentScrollHeight',
                    `${productMainContentRef.current.scrollHeight}px`
                )
            }
        }
        const resizeObserver = new ResizeObserver(() => {
            handleSizeChange()
        })
        resizeObserver.observe(productMainContentRef.current)
        window.addEventListener('resize', handleSizeChange)
        return () => {
            window.removeEventListener('resize', handleSizeChange)
            resizeObserver.disconnect()
        }
    }, [product, productMainContentRef])

    useEffect(() => {
        if (!locale || !product || isValidating) return
        const skusList = [
            product?.related_accessories_skus,
            product?.customers_may_also_like_skus,
            product?.bundle_and_save_skus,
            product?.elgato_bundle_and_save_skus
        ]
        const skus = grabAllSkus(skusList)
        getRelatedProductsData(skus, locale!).then((productsData) => {
            const [
                relatedProducts,
                CALProducts,
                BDSProducts,
                ElgBDSProducts
            ] = getProductsBySkus(productsData, skusList)
            if (relatedProducts?.length) {
                setRelatedAccessoriesData(relatedProducts)
            }
            if (CALProducts?.length) {
                setRelatedCustomerMayAlsoLikeData(CALProducts)
            }
            if (BDSProducts?.length) {
                setRelatedBundleAndSaveData(BDSProducts)
            }
            if (
                ElgBDSProducts?.length &&
                product?.elgato_bundle_and_save_informations
            ) {
                const validData = filterElgBDSProducts(
                    product?.elgato_bundle_and_save_informations,
                    product?.price_range,
                    ElgBDSProducts
                )
                setElgatoBDSData(validData)
            }
        })

        return () => {
            setElgatoBDSData([])
            setRelatedAccessoriesData([])
            setRelatedCustomerMayAlsoLikeData([])
            setRelatedBundleAndSaveData([])
        }
    }, [
        product?.bundle_and_save_skus,
        product?.related_accessories_skus,
        product?.customers_may_also_like_skus,
        product?.elgato_bundle_and_save_informations,
        product?.elgato_bundle_and_save_skus
    ])

    const { additionalImages, setAdditionalImages } = useProductUI(product)

    //route fallback
    if (router.isFallback) {
        return (
            <Container clean className={s['container-classes']}>
                <div>
                    {t('Page for this product was not generated beforehand.')}
                    <br />
                    {t('Loading...')}
                </div>
            </Container>
        )
    }

    if (!product) {
        return <NotFound />
    }

    //Error handler for PDP page
    if (!product && error) {
        return (
            <Container clean className={s['container-classes']}>
                <h1>{t('Error!!!')}</h1>
            </Container>
        )
    }

    //Warning if the product type is not supported
    if (!isSupportedProductType) {
        return (
            <Container clean className={s['container-classes']}>
                <h1>{`${product?.__typename} ${t('is not supported')}`}</h1>
            </Container>
        )
    }
    const { seoImage } = socialDefaultContent
    const socialContent: SocialDefaultContent = {
        ...socialDefaultContent,
        seoImage: productContentful?.socialDefaultMedia || seoImage
    }

    const noIndex = () => {
        const generatedSet: Set<string | undefined> = new Set(
            regionMapLanguages
                .filter(({ languages }) => languages !== undefined)
                .flatMap(({ languages, redirectRegion, region }) =>
                    languages?.map(
                        (language: any) =>
                            `/${redirectRegion || region}/${language}`
                    )
                )
        )

        const [lang, region] = locale?.split('-') || ['', '']
        if (!generatedSet.has(`/${region.toLowerCase()}/${lang}`)) {
            return true
        } else {
            return false
        }
    }

    return (
        <>
            <Container
                id="product-details"
                className={cn(
                    s['pdp-bg'],
                    {
                        [s[
                            `page-theme-${pageThemeContentful}`
                        ]]: pageThemeContentful
                    },
                    'pdp-details'
                )}
                clean
            >
                <YotpoScripts
                    enabled={config.base.yotpo.enabled}
                    apiKey={config.base.yotpo.api_key}
                />
                <div className={s['container-classes']}>
                    <SEO
                        product={yotpoJsonLd({ product, review })}
                        productContentful={
                            productContentful as IProductContentfulResponse<TypeComponentsTab>
                        }
                        socialDefaultContent={socialContent}
                        titleOverride={productContentful?.metaTitle}
                        descriptionOverride={productContentful?.metaDescription}
                        videoSchema={productContentful?.videoSchema}
                    />
                    <Head>
                        {/* Tell the browser to never restore the scroll position on load */}
                        <Script
                            dangerouslySetInnerHTML={{
                                __html: `history.scrollRestoration = "manual"`
                            }}
                        />
                        {!noIndex() &&
                            countriesLanguageAvailable?.map((country: any) => {
                                return country?.redirectRegion ? (
                                    <link
                                        key={`${country?.redirectLanguage}-${country.region}`}
                                        rel={rel}
                                        hrefLang={`${country.redirectLanguage}-${country.region}`}
                                        href={`${origin}${country?.redirectRegion}/${country?.redirectLanguage}${path}`}
                                    />
                                ) : (
                                    country.languages.map((language: any) => {
                                        const href = `${origin}${country.region}/${language}${path}`
                                        const hrefLang =
                                            country.region === 'ww'
                                                ? language
                                                : `${language}-${country.region}`
                                        return (
                                            <link
                                                key={hrefLang}
                                                rel={rel}
                                                hrefLang={hrefLang}
                                                href={href}
                                            />
                                        )
                                    })
                                )
                            })}
                        <link
                            rel="preconnect"
                            href="https://res.cloudinary.com"
                        />
                        <link
                            rel="dns-prefetch"
                            href="https://res.cloudinary.com"
                        />
                        {!!mediaGalleryEntries?.[0]?.url && (
                            <link
                                rel="preload"
                                href={mediaGalleryEntries[0].url}
                                as="image"
                            />
                        )}
                    </Head>
                    <Script
                        id="preload-first-image"
                        strategy="beforeInteractive"
                        dangerouslySetInnerHTML={{
                            __html: `
                              const link = document.createElement('link');
                              link.rel = 'preload';
                              link.as = 'image';
                              link.href = '${mediaGalleryEntries?.[0]?.url}';
                              document.head.appendChild(link);
                            `
                        }}
                    />
                    <BazaarLoader />
                    <div className={cn(s['pdp-container'], 'full-screen-mode')}>
                        <div className={s['pdp-left-container']}>
                            <ProductGallery
                                key={router.asPath}
                                mediaGalleryEntries={mediaGalleryEntries?.map(
                                    (asset: any) => {
                                        if (!asset?.url || asset?.url === '') {
                                            return {
                                                ...asset,
                                                url: `${
                                                    typeof window !==
                                                    'undefined'
                                                        ? window.location.origin
                                                        : ''
                                                }/images/default-product-image.png`
                                            }
                                        } else {
                                            asset.url = convertImageFormat(
                                                asset.url,
                                                'c_pad,q_auto,h_256,w_256',
                                                'webp'
                                            )
                                            return asset
                                        }
                                    }
                                )}
                                additionalImages={additionalImages}
                                setAdditionalImages={setAdditionalImages}
                                productContentful={productContentful}
                                config={{
                                    mobile: {
                                        gallerytype: GalleryTypeEnum.SCROLL,
                                        zoom: false,
                                        loop: false,
                                        fullscreen: false,
                                        sliderProps: {
                                            main: {
                                                navigation: false,
                                                pagination: false,
                                                allowTouchMove: true
                                            }
                                        }
                                    },
                                    desktop: {
                                        gallerytype: GalleryTypeEnum.SCROLL,
                                        zoom: false,
                                        loop: false,
                                        fullscreen: true,
                                        thumb: true,
                                        sliderProps: {
                                            main: {
                                                navigation: false,
                                                pagination: false,
                                                maxSlides: 5
                                            }
                                        }
                                    }
                                }}
                                pageTheme={productContentful?.pageTheme}
                            />
                            {isViewArVisible && (
                                <div
                                    className={s['pdp-view-ar']}
                                    id={PRODUCT_VIEW_AR_ID}
                                />
                            )}
                        </div>
                        <ProductConfigurator
                            key={router.asPath}
                            productContentful={productContentful}
                            relatedAccessoriesSkus={
                                relatedAccessoriesData?.length
                                    ? relatedAccessoriesData
                                    : relatedAccessoriesSkus
                            }
                            relatedCustomerMayAlsoLikeSkus={
                                relatedCustomerMayAlsoLikeData?.length
                                    ? relatedCustomerMayAlsoLikeData
                                    : relatedCustomerMayAlsoLikeSkus
                            }
                            relatedBundleAndSaveSkus={
                                relatedBundleAndSaveData?.length
                                    ? relatedBundleAndSaveData
                                    : relatedBundleAndSaveSkus
                            }
                            elgatoBundleAndSaveItems={
                                elgatoBDSData?.length
                                    ? elgatoBDSData
                                    : elgatoBundleAndSaveItems
                            }
                            product={product}
                            isConfig={isConfig}
                            isGiftCard={isGiftCard}
                            sku={productData?.productDetail?.items[0].sku}
                            productMainContentRef={productMainContentRef}
                            additionalImages={additionalImages}
                            setAdditionalImages={setAdditionalImages}
                        />
                    </div>
                </div>
            </Container>
            {productContentful?.contentModules && (
                <PDPProductProvider product={product}>
                    <ContentPage
                        pageContent={convertProductContentfulToContentPage(
                            productContentful!
                        )}
                    />
                </PDPProductProvider>
            )}
        </>
    )
}

PDP.Layout = MainLayout
