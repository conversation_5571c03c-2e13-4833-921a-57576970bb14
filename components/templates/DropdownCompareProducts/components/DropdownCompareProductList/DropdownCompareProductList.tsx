import ChevronRightIcon from '@components/atoms/Icon/general/ChevronRightIcon'
import CloseIconSimple from '@components/atoms/Icon/general/CloseIconSimple'
import { useLayoutContext } from '@components/layouts/MainLayout/LayoutContext'
import { Button } from '@components/molecules/Button/Button'
import { ProductProps } from '@components/templates/CompareProducts/components/CompareProductList/CompareProductList'
import { defaultLocale } from '@config/index'
import { getProducts, Products } from '@pylot-data/api/operations/get-products'
import { useMobile } from '@pylot-data/hooks/use-mobile'
import cn from 'classnames'
import { useTranslation } from 'next-i18next'
import { useRouter } from 'next/router'
import React, {
    FC,
    useCallback,
    useEffect,
    useMemo,
    useRef,
    useState
} from 'react'
import DropdownCompareProduct from '../DropdownCompareProduct/DropdownCompareProduct'
import DropdownCompareProductCategorySectionFeature from '../DropdownCompareProductCategorySectionFeature/DropdownCompareProductCategorySectionFeature'
import ProductSelector from '../ProductSelector/ProductSelector'
import s from './DropdownCompareProductList.module.scss'

export interface DropdownCompareProductListProps {
    content: ProductProps[]
    maxComparisons: number
    initialProductCount: number
}

export interface AddProductSlotProps {
    onAddProduct: () => void
    canAddMore: boolean
    disabled?: boolean
}

// Helper to normalize feature keys
const normalize = (str: string) => (str || '').trim().toLowerCase()

const getFeatureForProduct = (
    product: any,
    categoryTitle: string,
    featureHeadline: string
) => {
    if (!product || !product.productCategory) return null

    const category = product.productCategory.find(
        (c: any) => c.categoryTitle === categoryTitle
    )
    if (!category) return null

    const features = category.feature || category.featureList || []
    const isNested = features.length > 0 && Array.isArray(features[0]?.features)
    const flatFeatures = isNested
        ? features.flatMap((f: any) => f.features)
        : features

    const key = normalize(featureHeadline)
    return flatFeatures.find((f: any) => normalize(f.headline) === key) || null
}

// Helper to normalize feature values for comparison
const normalizeFeatureValue = (feature: any): string => {
    if (!feature) return 'empty'

    // Handle different feature value types
    if (feature.featureInfo && typeof feature.featureInfo === 'string') {
        return normalize(feature.featureInfo)
    }

    if (feature.isCompatible === true) return 'compatible'
    if (feature.isCompatible === false) return 'not-compatible'

    // Handle other feature properties that might indicate value
    if (feature.headline) return normalize(feature.headline)
    if (feature.bodyCopy) return normalize(feature.bodyCopy)

    return 'empty'
}

// Function to get feature rows that have differences between selected products
const getFeatureRowsWithDifferences = (
    selectedProducts: ProductProps[],
    unifiedFeatureData: any[]
): Array<{
    categoryId: string
    categoryTitle: string
    featureHeadline: string
}> => {
    const featureRowsWithDifferences: Array<{
        categoryId: string
        categoryTitle: string
        featureHeadline: string
    }> = []

    // Get only products that are actually selected (not null)
    const actualSelectedProducts = selectedProducts.filter(
        (product) => product !== null
    )

    // Need at least 2 products to compare
    if (actualSelectedProducts.length < 2) {
        return featureRowsWithDifferences
    }

    for (const category of unifiedFeatureData) {
        for (const feature of category.features) {
            const featureHeadline = feature.headline || feature.headLine
            if (!featureHeadline) continue

            // Get feature values for all selected products
            const featureValues = actualSelectedProducts.map((product) => {
                const productFeature = getFeatureForProduct(
                    product,
                    category.categoryTitle,
                    featureHeadline
                )
                return normalizeFeatureValue(productFeature)
            })

            // Check if there are differences in feature values
            const uniqueValues = new Set(featureValues)
            if (uniqueValues.size > 1) {
                featureRowsWithDifferences.push({
                    categoryId: category.id,
                    categoryTitle: category.categoryTitle,
                    featureHeadline: featureHeadline
                })
            }
        }
    }

    return featureRowsWithDifferences
}

const DropdownCompareProductList: FC<DropdownCompareProductListProps> = ({
    content,
    maxComparisons,
    initialProductCount
}) => {
    const { t } = useTranslation(['common'])
    const { pageTheme } = useLayoutContext()
    const { locale = 'en' } = useRouter()
    const [productsData, setProductsData] = useState<Products[]>([])

    const [selectedProducts, setSelectedProducts] = useState<ProductProps[]>(
        () => {
            const slots: ProductProps[] = []
            for (
                let i = 0;
                i <
                Math.min(initialProductCount, content.length, maxComparisons);
                i++
            ) {
                slots.push(content[i])
            }
            return slots
        }
    )

    const activeSlots = selectedProducts.length

    useEffect(() => {
        if (content && content.length > 0) {
            getProducts(
                content.map((product: ProductProps) => product?.sku),
                null,
                locale || defaultLocale || 'en-US'
            ).then((products) => {
                if (products && products.length) {
                    setProductsData(products)
                }
            })
        }
    }, [locale, content])

    const handleProductSelect = useCallback(
        (slotIndex: number, product: ProductProps) => {
            setSelectedProducts((prev) => {
                if (slotIndex === prev.length) {
                    return [...prev, product]
                }
                return prev.map((sp, index) => {
                    if (index === slotIndex) {
                        return product
                    }
                    return sp
                })
            })
        },
        []
    )

    const [isAddDropdownVisible, setIsAddDropdownVisible] = useState(false)

    const handleAddProductSlot = useCallback(() => {
        setIsAddDropdownVisible(true)
    }, [])

    const handleRemoveProductSlot = useCallback(
        (slotIndex: number) => {
            if (activeSlots > 2) {
                setSelectedProducts((prev) =>
                    prev.filter((sp, index) => index !== slotIndex)
                )
            }
        },
        [activeSlots]
    )

    const canAddMore =
        activeSlots < maxComparisons && activeSlots < content.length
    const canRemove = activeSlots > 2

    // Create a unified list of all features from all products
    const unifiedFeatureData = useMemo(() => {
        const categoriesMap = new Map()

        for (const product of content) {
            if (!product.productCategory) continue

            for (const category of product.productCategory) {
                const categoryTitle = category.categoryTitle
                if (!categoryTitle) continue

                if (!categoriesMap.has(categoryTitle)) {
                    categoriesMap.set(categoryTitle, {
                        categoryTitle,
                        id: category.id,
                        features: new Map()
                    })
                }

                const featuresMap = categoriesMap.get(categoryTitle).features
                const featureList =
                    category.feature || category.featureList || []
                const isNested =
                    featureList.length > 0 &&
                    Array.isArray(featureList[0]?.features)
                const allFeatures = isNested
                    ? featureList.flatMap((fl: any) => fl.features)
                    : featureList

                for (const feature of allFeatures) {
                    const headline = feature.headline || feature.headLine
                    const key = normalize(headline)
                    if (key && !featuresMap.has(key)) {
                        featuresMap.set(key, feature)
                    }
                }
            }
        }

        return Array.from(categoriesMap.values()).map((categoryData: any) => ({
            ...categoryData,
            features: Array.from(categoryData.features.values())
        }))
    }, [content])

    const [expandedCategories, setExpandedCategories] = useState<Set<string>>(
        new Set()
    )

    useEffect(() => {
        if (unifiedFeatureData.length > 0) {
            // Expand all categories by default
            setExpandedCategories(
                new Set(unifiedFeatureData.map((category: any) => category.id))
            )
        }
    }, [unifiedFeatureData])

    const [showDifferences, setShowDifferences] = useState<boolean>(false)

    const handleToggleDifferences = useCallback(() => {
        setShowDifferences((prev) => !prev)
    }, [])

    // Get feature rows that have differences between selected products
    const featureRowsWithDifferences = useMemo(() => {
        return getFeatureRowsWithDifferences(
            selectedProducts,
            unifiedFeatureData
        )
    }, [selectedProducts, unifiedFeatureData])

    // Helper function to check if a specific feature has differences
    const hasFeatureDifferences = useCallback(
        (categoryTitle: string, featureHeadline: string) => {
            return featureRowsWithDifferences.some(
                (row) =>
                    row.categoryTitle === categoryTitle &&
                    row.featureHeadline === featureHeadline
            )
        },
        [featureRowsWithDifferences]
    )

    // Log the feature rows with differences for debugging
    console.log('Feature rows with differences:', featureRowsWithDifferences)

    const toggleCategory = useCallback((categoryId: string) => {
        setExpandedCategories((prev) => {
            const newSet = new Set(prev)
            if (newSet.has(categoryId)) {
                newSet.delete(categoryId)
            } else {
                newSet.add(categoryId)
            }
            return newSet
        })
    }, [])

    const totalColumns = 1 + selectedProducts.length + (canAddMore ? 1 : 0)
    const gridTemplateColumns = `repeat(${totalColumns}, minmax(0, 1fr))`
    const gridTemplateColumnsMobile = `repeat(2, minmax(0, 1fr))`
    const { isMobile } = useMobile()

    const headerRef = useRef<HTMLDivElement>(null)

    useEffect(() => {
        const header = headerRef.current
        if (!header) return

        const updateHeight = () => {
            const height = header.offsetHeight
            document.documentElement.style.setProperty(
                '--compare-header-height',
                `${height}px`
            )
        }

        updateHeight()

        const resizeObserver = new ResizeObserver(updateHeight)
        resizeObserver.observe(header)

        return () => resizeObserver.disconnect()
    }, [])

    return (
        <section className={cn(s['dropdown-compare-product-list'])}>
            {/* Product Header Selection */}
            {/* {isMobile && (
                <div
                    className={cn(
                        s['dropdown-compare-product-list__slot'],
                        'md-max:hidden'
                    )}
                >
                    <div
                        className={cn(
                            s[
                                'dropdown-compare-product-list__toggle-container'
                            ],
                            'flex items-center gap-8px'
                        )}
                    >
                        <div
                            className={cn(
                                s[
                                    'dropdown-compare-product-list__toggle-track'
                                ],
                                {
                                    [s[
                                        'dropdown-compare-product-list__toggle-track--active'
                                    ]]: showDifferences
                                }
                            )}
                            onClick={handleToggleDifferences}
                            role="button"
                            tabIndex={0}
                            onKeyDown={(e) => {
                                if (e.key === 'Enter' || e.key === ' ') {
                                    e.preventDefault()
                                    handleToggleDifferences()
                                }
                            }}
                        >
                            <div
                                className={cn(
                                    s[
                                        'dropdown-compare-product-list__toggle-thumb'
                                    ],
                                    {
                                        [s[
                                            'dropdown-compare-product-list__toggle-thumb--active'
                                        ]]: showDifferences
                                    }
                                )}
                            />
                        </div>
                        <span
                            className={cn(
                                s['dropdown-compare-product-list__toggle-label']
                            )}
                        >
                            {t('Highlight differences')}
                        </span>
                    </div>
                </div>
            )} */}
            <div
                ref={headerRef}
                className={cn(
                    s['dropdown-compare-product-list__grid'],
                    s['dropdown-compare-product-list__grid-header'],
                    pageTheme === 'dark' ? 'bg-black' : 'bg-white',
                    'grid gap-16px sticky top-0 py-12px z-20'
                )}
                style={{
                    gridTemplateColumns: isMobile
                        ? gridTemplateColumnsMobile
                        : gridTemplateColumns
                }}
            >
                <div
                    className={cn(
                        s['dropdown-compare-product-list__slot'],
                        'md-max:order-3'
                    )}
                >
                    <div
                        className={cn(
                            s[
                                'dropdown-compare-product-list__toggle-container'
                            ],
                            'flex items-center gap-8px'
                        )}
                    >
                        <div
                            className={cn(
                                s[
                                    'dropdown-compare-product-list__toggle-track'
                                ],
                                {
                                    [s[
                                        'dropdown-compare-product-list__toggle-track--active'
                                    ]]: showDifferences
                                }
                            )}
                            onClick={handleToggleDifferences}
                            role="button"
                            tabIndex={0}
                            onKeyDown={(e) => {
                                if (e.key === 'Enter' || e.key === ' ') {
                                    e.preventDefault()
                                    handleToggleDifferences()
                                }
                            }}
                        >
                            <div
                                className={cn(
                                    s[
                                        'dropdown-compare-product-list__toggle-thumb'
                                    ],
                                    {
                                        [s[
                                            'dropdown-compare-product-list__toggle-thumb--active'
                                        ]]: showDifferences
                                    }
                                )}
                            />
                        </div>
                        <span
                            className={cn(
                                s['dropdown-compare-product-list__toggle-label']
                            )}
                        >
                            {t('Highlight differences')}
                        </span>
                    </div>
                </div>

                {selectedProducts.map((selectedProduct, index) => (
                    <div
                        key={selectedProduct.sku}
                        className={cn(
                            s['dropdown-compare-product-list__slot'],
                            'relative'
                        )}
                    >
                        {canRemove && index === selectedProducts.length - 1 && (
                            <Button
                                variant="tertiary"
                                color={pageTheme === 'dark' ? 'light' : 'dark'}
                                onClick={() => handleRemoveProductSlot(index)}
                                label={t('Remove')}
                                className={cn(
                                    s[
                                        'dropdown-compare-product-list__slot__remove-btn'
                                    ],
                                    'absolute top-8px left-8px z-10'
                                )}
                            >
                                <CloseIconSimple />
                            </Button>
                        )}

                        <div className="flex flex-col gap-8px">
                            {selectedProduct && (
                                <DropdownCompareProduct
                                    product={selectedProduct}
                                    productsData={productsData}
                                />
                            )}

                            <ProductSelector
                                availableProducts={content}
                                selectedProducts={selectedProducts}
                                onProductSelect={handleProductSelect}
                                slotIndex={index}
                                placeholder={t('Select a product to compare')}
                            />
                        </div>
                    </div>
                ))}
                {canAddMore && (
                    <div className="flex flex-col gap-8px md-max:hidden">
                        <div
                            className={cn(
                                s[
                                    'dropdown-compare-product-list__add-slot__button-wrapper'
                                ],
                                'rounded-xxxl'
                            )}
                        >
                            {!isAddDropdownVisible && (
                                <button
                                    className={cn(s['h-full w-full'])}
                                    onClick={handleAddProductSlot}
                                >
                                    + {t('Add Product')}
                                </button>
                            )}
                        </div>
                        {isAddDropdownVisible && (
                            <ProductSelector
                                availableProducts={content}
                                selectedProducts={selectedProducts}
                                onProductSelect={handleProductSelect}
                                slotIndex={selectedProducts.length}
                                placeholder={t('Select a product to compare')}
                            />
                        )}
                    </div>
                )}
            </div>

            {/* Feature Comparison Grid */}
            <div className={s['feature-grid-wrapper']}>
                {unifiedFeatureData.map((category: any) => {
                    const isExpanded = expandedCategories.has(category.id)
                    return (
                        <div
                            key={category.id}
                            className={cn(s['feature-category'], 'mb-12px')}
                        >
                            <div
                                className={cn(
                                    s['feature-category__header'],
                                    'flex items-center gap-6px bg-primitive-gray-10 cursor-pointer user-select-none sticky z-0',
                                    pageTheme === 'dark'
                                        ? 'bg-black'
                                        : 'bg-white'
                                )}
                                onClick={() => toggleCategory(category.id)}
                                role="button"
                                tabIndex={0}
                                onKeyDown={(e) => {
                                    if (e.key === 'Enter' || e.key === ' ') {
                                        e.preventDefault()
                                        toggleCategory(category.id)
                                    }
                                }}
                            >
                                <ChevronRightIcon
                                    className={cn(
                                        s['feature-category__chevron'],
                                        {
                                            [s[
                                                'feature-category__chevron--expanded'
                                            ]]: isExpanded
                                        }
                                    )}
                                />
                                <h5
                                    className={cn(
                                        s['feature-category__title'],
                                        'text-primitive-gray-90'
                                    )}
                                >
                                    {category.categoryTitle}
                                </h5>
                            </div>
                            <div
                                className={cn(
                                    s['feature-rows-container'],
                                    'max-h-0 overflow-hidden transition-max-height duration-300',
                                    {
                                        [s[
                                            'feature-rows-container--expanded'
                                        ]]: isExpanded
                                    }
                                )}
                            >
                                {category.features.map(
                                    (feature: any, featureIndex: number) => {
                                        const featureHeadline =
                                            feature.headline || feature.headLine
                                        const hasDifferences =
                                            showDifferences &&
                                            hasFeatureDifferences(
                                                category.categoryTitle,
                                                featureHeadline
                                            )

                                        // Check if the next feature has differences
                                        const nextFeature =
                                            category.features[featureIndex + 1]
                                        const nextFeatureHeadline =
                                            nextFeature?.headline ||
                                            nextFeature?.headLine
                                        const nextHasDifferences = nextFeature
                                            ? showDifferences &&
                                              hasFeatureDifferences(
                                                  category.categoryTitle,
                                                  nextFeatureHeadline
                                              )
                                            : false

                                        return isMobile ? (
                                            <div
                                                key={featureHeadline}
                                                className={cn(
                                                    s['feature-row'],
                                                    'flex flex-col p-16px gap-4px px-8px py-12px md:gap-16px',
                                                    {
                                                        [s[
                                                            'feature-row--different'
                                                        ]]: hasDifferences
                                                    },
                                                    {
                                                        'border-b border-primitive-gray-30':
                                                            !hasDifferences &&
                                                            !nextHasDifferences
                                                    }
                                                )}
                                            >
                                                {/* Mobile Headline */}
                                                <div
                                                    className={cn(
                                                        s['feature-cell'],
                                                        'w-full'
                                                    )}
                                                >
                                                    <DropdownCompareProductCategorySectionFeature
                                                        feature={feature}
                                                        displayMode="headline"
                                                        isDifferent={
                                                            hasDifferences
                                                        }
                                                    />
                                                </div>

                                                {/* Mobile Product Values */}
                                                <div className="flex gap-16px w-full justify-around">
                                                    {selectedProducts
                                                        .slice(0, 2)
                                                        .map(
                                                            (
                                                                product,
                                                                pIndex
                                                            ) => {
                                                                const productFeature = getFeatureForProduct(
                                                                    product,
                                                                    category.categoryTitle,
                                                                    feature.headline
                                                                )
                                                                const value =
                                                                    productFeature?.featureInfo ||
                                                                    productFeature?.featureInfoWithLabel
                                                                        ?.map(
                                                                            (item: {
                                                                                label: string
                                                                            }) =>
                                                                                item.label
                                                                        )
                                                                        .join(
                                                                            ', '
                                                                        ) ||
                                                                    '—'
                                                                return (
                                                                    <div
                                                                        key={
                                                                            pIndex
                                                                        }
                                                                        className={cn(
                                                                            s[
                                                                                'feature-cell'
                                                                            ],
                                                                            'text-center w-full'
                                                                        )}
                                                                    >
                                                                        <DropdownCompareProductCategorySectionFeature
                                                                            feature={
                                                                                productFeature
                                                                            }
                                                                            displayMode="value"
                                                                            isDifferent={
                                                                                hasDifferences
                                                                            }
                                                                        />
                                                                    </div>
                                                                )
                                                            }
                                                        )}
                                                </div>
                                            </div>
                                        ) : (
                                            <div
                                                key={featureHeadline}
                                                className={cn(
                                                    s['feature-row'],
                                                    'grid items-center p-16px gap-16px',
                                                    {
                                                        [s[
                                                            'feature-row--different'
                                                        ]]: hasDifferences
                                                    },
                                                    {
                                                        'border-b border-primitive-gray-30':
                                                            !hasDifferences &&
                                                            !nextHasDifferences
                                                    }
                                                )}
                                                style={{
                                                    gridTemplateColumns
                                                }}
                                            >
                                                {/* Desktop Headline Column */}
                                                <div
                                                    className={
                                                        s['feature-cell']
                                                    }
                                                >
                                                    <DropdownCompareProductCategorySectionFeature
                                                        feature={feature}
                                                        displayMode="headline"
                                                        isDifferent={
                                                            hasDifferences
                                                        }
                                                    />
                                                </div>

                                                {/* Desktop Product Value Columns */}
                                                {selectedProducts.map(
                                                    (product, pIndex) => {
                                                        const productFeature = getFeatureForProduct(
                                                            product,
                                                            category.categoryTitle,
                                                            feature.headline
                                                        )
                                                        return (
                                                            <div
                                                                key={pIndex}
                                                                className={
                                                                    s[
                                                                        'feature-cell'
                                                                    ]
                                                                }
                                                            >
                                                                <DropdownCompareProductCategorySectionFeature
                                                                    feature={
                                                                        productFeature
                                                                    }
                                                                    displayMode="value"
                                                                    isDifferent={
                                                                        hasDifferences
                                                                    }
                                                                />
                                                            </div>
                                                        )
                                                    }
                                                )}
                                            </div>
                                        )
                                    }
                                )}
                            </div>
                        </div>
                    )
                })}
            </div>
        </section>
    )
}

// Export utility functions for external use
export {
    getFeatureForProduct,
    getFeatureRowsWithDifferences,
    normalizeFeatureValue
}

export default React.memo(DropdownCompareProductList)
