.dropdown-compare-product-list__add-slot__button-wrapper {
    background: var(--primitive-gray-10);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    aspect-ratio: 285 / 140;
}

.feature-grid-wrapper {
    display: flex;
    flex-direction: column;
}

.feature-category {
    &__title {
        &:hover {
            color: var(--black);
        }
    }
}

.feature-category__header {
    top: var(--compare-header-height);
    padding: 16px 20px;
    border-radius: 16px;
    background-color: var(--primitive-gray-10);
    transition: background-color 0.2s ease;

    &:hover {
        background-color: var(--primitive-gray-20);
    }

    &:focus {
        outline: 2px solid var(--primitive-blue-500);
        outline-offset: -2px;
    }
}

.feature-category__chevron {
    transition: transform 0.2s ease;
    color: var(--primitive-gray-600);
    flex-shrink: 0;
}

.feature-category__chevron--expanded {
    transform: rotate(90deg);
}

.feature-rows-container--expanded {
    max-height: 2000px; // Large enough to accommodate content
}

.feature-row {
    &--different {
        background-color: #eff5ff;
        border: 1px solid var(--content-blue);
        border-radius: 16px;
        margin-top: 8px;
    }
}

.feature-cell {
    display: flex;
    align-items: center;

    &:first-child {
        justify-content: flex-start;
    }

    &:not(:first-child) {
        justify-content: center;
    }
}
.dropdown-compare-product-list {
    &__add-slot {
        background: var(--primitive-gray-800);
        border-color: var(--primitive-gray-600);

        &:hover {
            border-color: var(--primitive-blue-400);
            background: var(--primitive-gray-700);
        }
    }

    &__slot {
        /* Ensure the slot stretches full height of the row and lets its content sit at the bottom */
        @apply flex flex-col justify-end;

        &__remove-btn {
            min-height: 0 !important;
            svg {
                margin-top: 0 !important;
            }
        }
    }
    &__toggle-container {
    }
    &__toggle-track {
        background-color: var(--primitive-gray-20);
        border-radius: 12px;
        width: 28px;
        height: 16px;
        position: relative;
        cursor: pointer;
        transition: background-color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        &--active {
            background-color: var(--primitive-blue-90);
        }
    }
    &__toggle-thumb {
        background-color: var(--black);
        border-radius: 12px;
        width: 12px;
        height: 12px;
        position: absolute;
        top: 2px;
        left: 2px;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        transform: translateX(0);
        &--active {
            transform: translateX(12px);
            background-color: var(--white);
        }
    }
    &__toggle-label {
        color: var(--primitive-gray-200);
    }
}
// Dark theme
.dark {
    .feature-category {
        border-color: var(--primitive-gray-700);
    }

    .feature-category__header {
        background-color: var(--primitive-gray-800);

        &:hover {
            background-color: var(--primitive-gray-700);
        }
    }

    .feature-category__chevron {
        color: var(--primitive-gray-500);
    }

    .feature-row {
        border-bottom-color: var(--primitive-gray-700);

        &--different {
            background-color: var(--primitive-yellow-900);
            border-left-color: var(--primitive-yellow-400);

            &::before {
                color: var(--primitive-yellow-300);
            }
        }
    }

    .feature-cell {
        background-color: transparent;
    }

    .dropdown-compare-product-list {
        &__add-slot {
            background: var(--primitive-gray-800);
            border-color: var(--primitive-gray-600);

            &:hover {
                border-color: var(--primitive-blue-400);
                background: var(--primitive-gray-700);
            }
        }
        &__slot {
            &__remove-btn {
                border-color: red;
                background-color: black;

                &:hover {
                    background: var(--primitive-gray-800);
                }
            }
        }
    }
}
