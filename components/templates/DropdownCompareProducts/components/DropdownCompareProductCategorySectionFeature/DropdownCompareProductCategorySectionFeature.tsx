import CheckmarkIcon from '@components/atoms/Icon/pictograms/CheckmarkIcon'
import SubtractIcon from '@components/atoms/Icon/pictograms/SubtractIcon'
import { useLayoutContext } from '@components/layouts/MainLayout/LayoutContext'
import { FeatureIconText } from '@components/molecules/FeatureIconText/FeatureIconText'
import cn from 'classnames'
import { decode } from 'he'
import { FC } from 'react'
import s from './DropdownCompareProductCategorySectionFeature.module.scss'

interface DropdownCompareProductCategoryContent {
    feature: any
    displayMode: 'headline' | 'value'
    isDifferent: boolean
    product?: any
}

const DropdownCompareProductCategorySectionFeature: FC<DropdownCompareProductCategoryContent> = ({
    feature,
    displayMode,
    isDifferent
}) => {
    const { pageTheme } = useLayoutContext()

    if (!feature) {
        return (
            <div
                className={cn(
                    s['dropdown-compare-product-category-section-feature'],
                    s[
                        'dropdown-compare-product-category-section-feature--empty'
                    ]
                )}
            >
                <SubtractIcon />
            </div>
        )
    }

    const featureInfo = feature?.featureInfo
    const isCompatible = feature?.isCompatible
    const highlight = feature?.highlight
    const headline = feature?.headline
    const bodyCopy = feature?.bodyCopy
    const featureInfoWithLabel = feature?.featureInfoWithLabel
    const iconList = feature?.iconList
    const displayType = feature?.displayType

    const renderHeadline = () => (
        <div
            className={cn(
                s['dropdown-compare-product-category-section-feature__main']
            )}
        >
            <div
                className={cn(
                    s[
                        'dropdown-compare-product-category-section-feature__content'
                    ],
                    'md-max:pl-8px md-max:gap-8px'
                )}
            >
                {headline && (
                    <div
                        className={cn(
                            'text-body-copy-md-max md:text-body-copy font-bold',
                            {
                                'text-white': pageTheme === 'dark'
                            },
                            {
                                'text-black': pageTheme !== 'dark'
                            }
                        )}
                    >
                        {decode(headline)}
                    </div>
                )}
                {bodyCopy && (
                    <div className="text-small-copy-md-max md:text-small-copy">
                        {decode(bodyCopy)}
                    </div>
                )}
            </div>
        </div>
    )

    const renderValue = () => {
        if (featureInfoWithLabel && featureInfoWithLabel.length > 0) {
            return (
                <div className="flex justify-center flex-wrap gap-6px md:gap-12px w-full">
                    {featureInfoWithLabel.map((item: any, idx: number) => (
                        <div
                            key={`fiwl-${idx}`}
                            className="flex flex-col items-center"
                        >
                            {item.label && (
                                <span className="text-body-copy-md-max md:text-body-copy">
                                    {decode(item.label)}
                                </span>
                            )}
                            {item.badge && (
                                <span
                                    className={cn(
                                        s[
                                            'feature-info-with-label__badge-outer'
                                        ],
                                        {
                                            [s[
                                                'feature-info-with-label__badge-outer--hdr'
                                            ]]: item.badge === 'hdr'
                                        },
                                        {
                                            [s[
                                                'feature-info-with-label__badge-outer--sdr'
                                            ]]: item.badge === 'sdr'
                                        }
                                    )}
                                >
                                    <span
                                        className={cn(
                                            s[
                                                'feature-info-with-label__badge-inner'
                                            ],
                                            'uppercase text-xs-copy font-bold rounded-md font-bold',
                                            {
                                                'text-white bg-black':
                                                    pageTheme !== 'dark'
                                            }
                                        )}
                                    >
                                        {decode(item.badge)}
                                    </span>
                                </span>
                            )}
                        </div>
                    ))}
                </div>
            )
        }

        if (featureInfo && typeof featureInfo === 'string') {
            return (
                <div
                    className={cn(
                        'text-center flex flex-col w-full text-body-copy-md-max md:text-body-copy',
                        {
                            'text-primitive-gray-30': pageTheme === 'dark',
                            'text-primitive-gray-100': pageTheme !== 'dark',
                            'text-black': isDifferent && highlight,
                            'text-primitive-blue-110':
                                isDifferent && !highlight,
                            'font-bold': isDifferent
                        }
                    )}
                >
                    {iconList && iconList.length > 0 && (
                        <div className="flex items-center w-full gap-8px mb-2 justify-center">
                            {iconList.map((iconName: string, idx: number) => (
                                <FeatureIconText
                                    key={`fiwl-${idx}`}
                                    icon={iconName}
                                    className={
                                        s[
                                            'dropdown-compare-product-category-section-feature__icon'
                                        ]
                                    }
                                />
                            ))}
                        </div>
                    )}
                    {decode(featureInfo)}
                </div>
            )
        }

        if (isCompatible === true) {
            return <CheckmarkIcon />
        }

        if (isCompatible === false) {
            return (
                <div className={s['feature-placeholder']}>
                    <SubtractIcon />
                </div>
            )
        }

        if (displayType === 'price') {
            return <div>price</div>
        }

        if (displayType === 'rating') {
            return <div>rating</div>
        }
    }

    return (
        <div
            className={cn(
                s['dropdown-compare-product-category-section-feature'],
                'md:p-8px w-full flex items-center justify-center',
                {
                    [s[
                        'dropdown-compare-product-category-section-feature--highlighted'
                    ]]: highlight && displayMode !== 'headline',
                    [s[
                        'dropdown-compare-product-category-section-feature--dark'
                    ]]: pageTheme === 'dark',
                    [s[
                        'dropdown-compare-product-category-section-feature--different'
                    ]]: isDifferent
                }
            )}
        >
            {displayMode === 'headline' ? renderHeadline() : renderValue()}
            {highlight && displayMode !== 'headline' && (
                <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="14"
                    height="12"
                    viewBox="0 0 14 12"
                    fill="none"
                >
                    <path
                        d="M6.39173 0.365029C6.64055 -0.121676 7.35945 -0.121677 7.60827 0.365028L9.24922 3.57487L12.9185 4.08959C13.4749 4.16764 13.697 4.8277 13.2944 5.20654L10.6393 7.70506L11.2661 11.233C11.3611 11.768 10.7795 12.1759 10.2819 11.9233L7 10.2577L3.71811 11.9233C3.22048 12.1759 2.63887 11.768 2.73391 11.233L3.36069 7.70506L0.705585 5.20654C0.302993 4.8277 0.525148 4.16764 1.08152 4.08959L4.75078 3.57487L6.39173 0.365029Z"
                        fill="#204CFE"
                    />
                </svg>
            )}
        </div>
    )
}

export default DropdownCompareProductCategorySectionFeature
