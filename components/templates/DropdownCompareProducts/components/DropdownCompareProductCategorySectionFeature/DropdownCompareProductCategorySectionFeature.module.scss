.dropdown-compare-product-category-section-feature {
    min-height: 40px;
    transition: all 0.2s ease;

    &--empty,
    .feature-placeholder {
        color: var(--primitive-gray-500);
        font-size: 14px;
        justify-content: center;
        width: 100%;
        display: flex;
    }

    &--highlighted {
        min-height: 46px;
        align-items: flex-start;
        border-radius: 6px;
        background: var(--primitive-white);
        box-shadow: 0px 0px 2px 0px rgba(0, 0, 0, 0.1),
            0px 4px 8px 0px rgba(0, 0, 0, 0.1),
            0px 8px 16px 0px rgba(0, 0, 0, 0.05);
        @screen md-max {
            padding: 8px;
        }
    }

    &--dark {
        color: var(--primitive-white);
        &.dropdown-compare-product-category-section-feature--highlighted {
            background: var(--primitive-blue-900);
        }
    }

    &__main {
        display: flex;
        align-items: center;
        gap: 8px;
        width: 100%;
        line-height: 1.4;
    }

    &__icon {
        svg {
            height: 30px;
            width: fit-content;
        }
    }

    &__content {
        @screen md-max {
            @apply flex flex-col gap-8px;
        }
    }
}

.feature-info-with-label {
    &__badge-outer {
        display: inline-block;
        padding: 2px; // border thickness
        border-radius: 6px;
        &--hdr {
            background: linear-gradient(180deg, #a638fe 0%, #204cfe 100%);
        }
        &--sdr {
            background: var(--primitive-gray-30);
        }
    }
    &__badge-inner {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 2px 4px;
        min-height: 18px;
    }
}
