import { useLayoutContext } from '@components/layouts/MainLayout/LayoutContext'
import {
    Dropdown,
    DropdownLabelValue
} from '@components/molecules/Dropdown/Dropdown'
import { ProductProps } from '@components/templates/CompareProducts/components/CompareProductList/CompareProductList'
import { defaultLocale } from '@config/index'
import { Products, getProducts } from '@pylot-data/api/operations/get-products'
import cn from 'classnames'
import { useTranslation } from 'next-i18next'
import { useRouter } from 'next/router'
import React, { FC, useEffect, useMemo, useState } from 'react'
import s from './ProductSelector.module.scss'

export interface ProductSelectorProps {
    availableProducts: ProductProps[]
    selectedProducts: ProductProps[]
    onProductSelect: (slotIndex: number, product: ProductProps) => void
    slotIndex: number
    placeholder?: string
    disabled?: boolean
}
const ProductSelector: FC<ProductSelectorProps> = ({
    availableProducts,
    selectedProducts,
    onProductSelect,
    slotIndex,
    placeholder = 'Select a product',
    disabled = false
}) => {
    const { pageTheme } = useLayoutContext()
    const { locale = 'en' } = useRouter()
    const { t } = useTranslation(['common'])
    const [productsData, setProductsData] = useState<Products[]>([])

    // Fetch product data for all available products
    useEffect(() => {
        if (availableProducts && availableProducts.length > 0) {
            getProducts(
                availableProducts.map((product) => product.sku),
                null,
                locale || defaultLocale || 'en-US'
            ).then((products) => {
                if (products && products.length) {
                    setProductsData(products)
                }
            })
        }
    }, [locale, availableProducts])

    // Create dropdown options from available products
    const dropdownOptions: DropdownLabelValue[] = useMemo(() => {
        return availableProducts
            .filter((product) => {
                return !selectedProducts.some(
                    (sp, spIndex) => sp.sku === product.sku
                )
            })
            .map((product) => {
                const productData = productsData.find(
                    (item) => item.productSku === product.sku
                )
                return {
                    label: productData?.productData?.[0]?.name || '',
                    value: product.sku
                }
            })
    }, [availableProducts, productsData, selectedProducts])

    const getCurrentSelectedOption = () => {
        const currentSelectedProduct = selectedProducts.find(
            (sp, index) => index === slotIndex
        )
        if (!currentSelectedProduct?.sku) {
            return { label: placeholder, value: '' }
        }
        const productData = productsData.find(
            (item) => item.productSku === currentSelectedProduct.sku
        )
        return {
            label:
                productData?.productData?.[0]?.name ||
                currentSelectedProduct.sku,
            value: currentSelectedProduct.sku
        }
    }

    const currentOption = getCurrentSelectedOption()

    const handleProductChange = (sku: string) => {
        const selectedProduct = availableProducts.find(
            (product) => product.sku === sku
        )
        if (selectedProduct) {
            onProductSelect(slotIndex, selectedProduct)
        }
    }

    const displayTitle = currentOption ? currentOption.label : placeholder

    return (
        <div
            className={cn(s['product-selector'], {
                [s['product-selector--disabled']]: disabled
            })}
        >
            <Dropdown
                title={displayTitle}
                currentOption={currentOption}
                variant="primary"
                buttonColor={pageTheme === 'dark' ? 'dark-grey' : 'gray-10'}
                className={cn(s['product-selector__dropdown'], {
                    [s['product-selector__dropdown--dark']]:
                        pageTheme === 'dark',
                    [s['product-selector__dropdown--light']]:
                        pageTheme !== 'dark'
                })}
                options={[
                    {
                        title: '',
                        options: dropdownOptions
                    }
                ]}
                onChange={handleProductChange}
                disabled={disabled}
            />
        </div>
    )
}

export default React.memo(ProductSelector)
