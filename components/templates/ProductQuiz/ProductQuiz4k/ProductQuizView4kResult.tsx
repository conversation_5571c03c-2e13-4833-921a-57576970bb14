import { ProductQuizView } from '@components/templates/ProductQuiz/ProductQuiz'
import cn from 'classnames'
import { useTranslation } from 'next-i18next'
import { FC } from 'react'
import { IconButton } from '../ProductQuizSD/IconButton'
import s from './ProductQuizView4kResult.module.scss'
import { Button } from '@components/molecules/Button/Button'
import { Icon } from '@components/atoms/Icon/Icon'

type ProductQuizViewSDResultProps = ProductQuizView & {
    selectedSelectors: string[]
    isBackDisabled: boolean
    onBackClick: () => void
    onRestartClick: () => void
}

const ProductQuizView4kResult: FC<ProductQuizViewSDResultProps> = ({
    textPanel,
    selectedSelectors,
    links,
    isBackDisabled,
    onBackClick,
    onRestartClick
}) => {
    const { t } = useTranslation(['common'])
    return (
        <div className="flex flex-col flex-1 gap-40px">
            <div className="flex flex-col items-center text-center gap-16px md:gap-24px mx-auto max-w-textContainer">
                <p className="bold text-primitive-gray-100">
                    {t('Your input')}:
                </p>
                <div className="flex gap-8px md:gap-12px">
                    {selectedSelectors.map((selector, index) => (
                        <div
                            className={cn(
                                s['selector'],
                                'button-text text-primitive-gray-100'
                            )}
                            key={index}
                        >
                            {selector}
                        </div>
                    ))}
                </div>
            </div>
            <div className="flex flex-col gap-24px m-auto items-center max-w-textContainer">
                <div className="flex flex-col gap-16px md:gap-24px text-center">
                    {(!!textPanel?.calloutTitle || !!textPanel?.headline) && (
                        <div className="flex flex-col gap-8px md:gap-16px">
                            {!!textPanel?.calloutTitle && (
                                <h4>{textPanel.calloutTitle}</h4>
                            )}
                            {!!textPanel?.headline && (
                                <h1>{textPanel.headline}</h1>
                            )}
                        </div>
                    )}
                    {!!textPanel?.bodyCopy && (
                        <p className="body-copy">{textPanel.bodyCopy}</p>
                    )}
                    {!!textPanel?.disclaimerText && (
                        <p className="small-copy italic">
                            {textPanel.disclaimerText}
                        </p>
                    )}
                </div>
                {links && (
                    <div className="flex gap-8px flex-wrap">
                        {links.map((link, index) => (
                            <Button
                                key={index}
                                variant={link.style}
                                color={link.styleColor}
                                href={link?.linkUrl}
                                label={link?.linkTitle}
                                dataLayer={link?.eventTracking}
                                iconAlignment={link.iconAlignment}
                            >
                                {link?.icon && <Icon name={link.icon} />}
                                {link?.linkTitle}
                            </Button>
                        ))}
                    </div>
                )}
            </div>
            <div className="flex gap-8px md:gap-16px justify-between">
                <div className="flex gap-8px md:gap-16px">
                    <IconButton
                        iconName="chevronLeft"
                        text={t('Back')}
                        iconAlignment="left"
                        isDisabled={isBackDisabled}
                        onClick={onBackClick}
                    />
                    <IconButton
                        className={cn(s['refresh-icon'], 'scale-x-[-1]')}
                        iconName="refreshIcon"
                        onClick={onRestartClick}
                    />
                </div>
            </div>
        </div>
    )
}

export default ProductQuizView4kResult
