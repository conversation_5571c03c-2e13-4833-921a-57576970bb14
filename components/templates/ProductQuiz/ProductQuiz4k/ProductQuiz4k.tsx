import s from './ProductQuiz4k.module.scss'
import { FC, useState } from 'react'
import {
    ProductQuizProps,
    ProductQuizView
} from '@components/templates/ProductQuiz/ProductQuiz'
import ProductQuizView4k from './ProductQuizView4k'
import ProductQuizView4kResult from './ProductQuizView4kResult'
import { IconButton } from '@components/templates/ProductQuiz/ProductQuizSD/IconButton'
import dynamic from 'next/dynamic'

const MediaWithContent = dynamic(
    () => import('@components/templates/MediaWithContent/MediaWithContent'),
    {
        ssr: false
    }
)

const ProgressBar = ({ progress }: { progress: number }) => {
    return (
        <div className={s['progress-bar']}>
            <div
                className={s['progress-bar__progress-indicator']}
                style={{ width: `${progress}%` }}
            />
        </div>
    )
}

const ProductQuiz4k: FC<ProductQuizProps> = ({ content }) => {
    const [isQuizPreview, setIsQuizPreview] = useState(true)
    const [selectedViewIndexes, setSelectedViewIndexes] = useState([0])
    const [currentSelectedViewIndex, setCurrentSelectedViewIndex] = useState<
        number | null
    >(null)

    const getCurrentView = () => {
        let view = content.productQuizView?.[0]
        for (let i = 1; i < selectedViewIndexes.length; i++) {
            const index = selectedViewIndexes[i]
            view = view?.productQuizView?.[index]
        }
        return view
    }

    const getMaxDepth = (view: ProductQuizView | undefined): number => {
        if (!view || !view.productQuizView?.length) return 1
        return 1 + Math.max(...view.productQuizView.map(getMaxDepth))
    }

    const getSelectedSelectors = () => {
        const selected = []
        let view = content.productQuizView?.[0]
        for (let i = 1; i < selectedViewIndexes.length; i++) {
            const index = selectedViewIndexes[i]
            view = view?.productQuizView?.[index]
            if (view?.selectorText) {
                selected.push(view?.selectorText)
            }
        }
        return selected
    }

    const currentView = getCurrentView()
    const currentDepth = selectedViewIndexes.length
    const totalDepth = currentDepth - 1 + getMaxDepth(currentView)
    const progressPercent = Math.round((currentDepth / totalDepth) * 100)
    const isNextDisabled = currentSelectedViewIndex === null
    const isBackDisabled = selectedViewIndexes.length <= 1

    const selectedSelectors = getSelectedSelectors()

    const onSelectViewIndex = (index: number) => {
        setCurrentSelectedViewIndex(index)
    }

    const onNextClick = () => {
        if (currentSelectedViewIndex === null) return
        setSelectedViewIndexes((prev) => [...prev, currentSelectedViewIndex])
        setCurrentSelectedViewIndex(null)
    }

    const onBackClick = () => {
        if (isBackDisabled) return
        const lastSelectedIndex =
            selectedViewIndexes[selectedViewIndexes.length - 1]
        setSelectedViewIndexes((prev) => prev.slice(0, -1))
        setCurrentSelectedViewIndex(lastSelectedIndex)
    }

    const onRestartClick = () => {
        setSelectedViewIndexes([0])
        setCurrentSelectedViewIndex(null)
    }

    const onCloseClick = () => {
        setIsQuizPreview(true)
        setSelectedViewIndexes([0])
        setCurrentSelectedViewIndex(null)
    }

    const mediaWithContentPreview = content.mediaWithContents?.[0]

    if (isQuizPreview && mediaWithContentPreview) {
        const textPanel = {
            ...mediaWithContentPreview.textPanel,
            onLinkClick: () => setIsQuizPreview(false)
        }
        const mediaWithContentProps = { ...mediaWithContentPreview, textPanel }
        return <MediaWithContent {...mediaWithContentProps} />
    }

    return (
        <div className={s['product-quiz-sd']}>
            <div className={s['product-quiz-sd__quiz-container']}>
                <div className="flex gap-16px md:gap-40px items-center">
                    <ProgressBar progress={progressPercent} />
                    <IconButton onClick={onCloseClick} iconName="close" />
                </div>
                {currentView && !currentView.variant && (
                    <ProductQuizView4k
                        {...currentView}
                        onSelectViewIndex={onSelectViewIndex}
                        currentSelectedViewIndex={currentSelectedViewIndex}
                        onNextClick={onNextClick}
                        onBackClick={onBackClick}
                        onRestartClick={onRestartClick}
                        isNextDisabled={isNextDisabled}
                        isBackDisabled={isBackDisabled}
                    />
                )}
                {currentView?.variant === '4k-result' && (
                    <ProductQuizView4kResult
                        {...currentView}
                        selectedSelectors={selectedSelectors}
                        onBackClick={onBackClick}
                        onRestartClick={onRestartClick}
                        isBackDisabled={isBackDisabled}
                    />
                )}
            </div>
        </div>
    )
}

export default ProductQuiz4k
