import React, { CSSProperties, FC, JSXElementConstructor } from 'react'
type ComponentType = string | JSXElementConstructor<{ className?: string }>
// Pictograms
import * as pictogram from '@components/atoms/Icon/pictograms'
// Product Icons
import * as products from '@components/atoms/Icon/products'
// General / System Icons
import * as general from '@components/atoms/Icon/general'
// Social Icons
import * as social from '@components/atoms/Icon/social'
// Circular Icons
import * as circularIcons from '@components/atoms/Icon/circularIcons'
// Icons used for comparison panel
import * as comparison from '@components/atoms/Icon/comparison'

export type IconComponentsMapping = {
    [key: string]: ComponentType
}

const components = {
    ...general,
    ...pictogram,
    ...products,
    ...social,
    ...circularIcons,
    ...comparison
} as IconComponentsMapping

// type IconName =
//     | keyof typeof pictogram
//     | keyof typeof products
//     | keyof typeof social
//     | keyof typeof circularIcons
//     | keyof typeof general

export type IconProps = {
    name: string
    className?: string
    style?: CSSProperties
}

export const Icon: FC<IconProps> = (props) => {
    const { name, className, ...rest } = props
    const component = components[name]
    if (!component) {
        return (
            <div
                data-title="Icon not found"
                className="border border-dashed border-yellow"
            >
                {name}
            </div>
        )
    }
    const IconComponent = component
    return <IconComponent className={className} {...rest} />
}
