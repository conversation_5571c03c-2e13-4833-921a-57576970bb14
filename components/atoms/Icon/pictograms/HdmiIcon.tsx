const HdmiIcon = ({ ...props }) => {
    return (
        <svg
            width="120"
            height="120"
            viewBox="0 0 120 120"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            {...props}
        >
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M103.332 46.6667H16.6654V68.3334H25.7421C26.9504 68.3334 28.136 68.6618 29.1721 69.2834L35.922 73.3334H84.0754L90.8253 69.2834C91.8614 68.6618 93.047 68.3334 94.2553 68.3334H103.332V46.6667ZM16.6654 43.3334C14.8244 43.3334 13.332 44.8258 13.332 46.6667V68.3334C13.332 70.1743 14.8244 71.6667 16.6654 71.6667H25.7421C26.3462 71.6667 26.939 71.8309 27.4571 72.1417L34.207 76.1917C34.725 76.5025 35.3178 76.6667 35.922 76.6667H84.0754C84.6796 76.6667 85.2723 76.5025 85.7904 76.1917L92.5403 72.1417C93.0584 71.8309 93.6512 71.6667 94.2553 71.6667H103.332C105.173 71.6667 106.665 70.1743 106.665 68.3334V46.6667C106.665 44.8258 105.173 43.3334 103.332 43.3334H16.6654Z"
                fill="currentColor"
            />
            <rect
                x="26.668"
                y="56.6666"
                width="66.6667"
                height="3.33333"
                rx="1.66667"
                fill="currentColor"
            />
        </svg>
    )
}

export default HdmiIcon
