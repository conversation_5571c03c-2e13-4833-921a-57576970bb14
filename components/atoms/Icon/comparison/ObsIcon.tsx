const ObsIcon = ({ ...props }) => {
    return (
        <svg
            width="30"
            height="30"
            viewBox="0 0 30 30"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            {...props}
        >
            <path
                d="M19.3689 11.4713C19.0478 12.0305 18.6177 12.5195 18.1041 12.9093C17.5905 13.2991 17.0038 13.5818 16.3789 13.7406C15.754 13.8994 15.1035 13.9311 14.4661 13.8338C13.8287 13.7365 13.2173 13.5122 12.6682 13.1741C12.264 12.9241 11.8983 12.6165 11.5827 12.2612C10.7519 11.3246 10.3192 10.1008 10.3767 8.85012C10.3836 8.71019 10.3953 8.57066 10.4136 8.43176C10.4317 8.29486 10.4558 8.15858 10.4856 8.02394C10.5161 7.88571 10.5528 7.74906 10.5955 7.61397C10.6369 7.48249 10.6841 7.353 10.7372 7.22549C10.7922 7.09292 10.8534 6.96316 10.9196 6.83584C10.9892 6.70175 11.0664 6.57131 11.1487 6.44467C11.2247 6.32788 11.3056 6.21459 11.3915 6.1048C11.4846 5.98514 11.5836 5.8697 11.6881 5.75975C11.7935 5.64877 11.9036 5.54273 12.0184 5.44164C12.1354 5.33861 12.258 5.24225 12.3843 5.15108C12.4495 5.10408 12.5158 5.05886 12.5834 5.01544C11.2106 5.71687 10.1664 6.92721 9.67384 8.38802C9.18124 9.84882 9.27927 11.4443 9.947 12.8338C9.96022 12.8613 9.97365 12.8886 9.9873 12.9159L10.006 12.953C10.009 12.9607 10.0128 12.9682 10.0174 12.9751C10.0242 12.9826 10.0265 12.9826 10.0348 12.9823L10.0391 12.9821C10.1014 12.981 10.1638 12.9813 10.2261 12.9826C10.3412 12.9851 10.4561 12.9917 10.5709 13.0023C11.632 13.1026 12.6301 13.5516 13.4091 14.279C14.1881 15.0065 14.7043 15.9715 14.8769 17.0233C14.9549 17.5045 14.9601 17.9947 14.8924 18.4774C14.7638 19.3879 14.3766 20.2423 13.7768 20.9392C13.4328 21.3385 13.0265 21.6795 12.5737 21.9491C11.6697 22.483 10.6163 22.7077 9.57307 22.5894C9.38442 22.5678 9.19718 22.5353 9.01229 22.492C8.89254 22.4639 8.77398 22.4309 8.65674 22.3936C9.18345 22.627 9.74169 22.7816 10.3135 22.8522C10.8469 22.918 11.3868 22.9116 11.9185 22.8329C12.8695 22.6912 13.7712 22.3187 14.545 21.748C15.1128 21.3295 15.6017 20.8133 15.9887 20.2236C16.0055 20.198 16.022 20.1723 16.0384 20.1465L16.0438 20.1387C16.0505 20.1294 16.0585 20.1182 16.0571 20.11C16.0507 20.0933 16.0422 20.0775 16.0317 20.063C15.9995 20.0024 15.9685 19.9412 15.9388 19.8793C15.8793 19.755 15.8251 19.6283 15.7764 19.4991C15.6777 19.2374 15.6022 18.9676 15.5506 18.6927C15.4402 18.1017 15.4406 17.4954 15.5516 16.9046C15.6461 16.4027 15.8204 15.9192 16.0678 15.4725C16.4806 14.7227 17.0858 14.0967 17.8212 13.6588C18.5566 13.221 19.3954 12.9872 20.2512 12.9816C20.3694 12.9808 20.4876 12.9842 20.6056 12.9919C20.7195 12.9994 20.833 13.0109 20.9461 13.0261C21.0581 13.0415 21.1695 13.0607 21.2803 13.084C21.3888 13.1069 21.4964 13.1334 21.6031 13.1635C21.7077 13.1932 21.8111 13.2264 21.9135 13.263C22.0173 13.3003 22.1197 13.3411 22.2207 13.3854C22.3236 13.4304 22.4248 13.4791 22.5243 13.5316C22.6236 13.5838 22.7211 13.6395 22.8167 13.6985C22.9065 13.754 22.9947 13.8122 23.0805 13.8738C23.1689 13.9372 23.2553 14.0034 23.3397 14.0721C23.4268 14.143 23.5108 14.2176 23.5926 14.2945C23.6796 14.3765 23.7634 14.4616 23.8441 14.5498C23.9246 14.6376 24.0017 14.7284 24.0754 14.8222C24.1576 14.9266 24.2359 15.0343 24.3089 15.1452C24.3839 15.2592 24.4549 15.3762 24.5203 15.496C24.6115 15.6632 24.6928 15.8357 24.7637 16.0124C24.84 16.2046 24.9044 16.4013 24.9564 16.6015C24.8157 15.5311 24.3837 14.5199 23.7074 13.6784C23.2102 13.0588 22.5937 12.5453 21.8944 12.1683C21.1772 11.7818 20.3875 11.5488 19.5754 11.4842C19.5066 11.4787 19.4378 11.4744 19.3689 11.4713Z"
                fill="currentColor"
            />
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M27.25 15C27.25 21.6274 21.8774 27 15.25 27C8.62258 27 3.25 21.6274 3.25 15C3.25 8.37258 8.62258 3 15.25 3C21.8774 3 27.25 8.37258 27.25 15ZM25.75 15C25.75 20.799 21.049 25.5 15.25 25.5C9.45101 25.5 4.75 20.799 4.75 15C4.75 9.20101 9.45101 4.5 15.25 4.5C21.049 4.5 25.75 9.20101 25.75 15Z"
                fill="currentColor"
            />
        </svg>
    )
}

export default ObsIcon
