const IpadOSIcon = ({ ...props }) => {
    return (
        <svg
            width="31"
            height="30"
            viewBox="0 0 31 30"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            {...props}
        >
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M1.13884 4.25547C0.25 5.96656 0.25 8.21455 0.25 12.7105V17.2895C0.25 21.7855 0.25 24.0334 1.13884 25.7445C1.88786 27.1865 3.06355 28.3621 4.50547 29.1112C6.21656 30 8.46455 30 12.9605 30H17.5395C22.0355 30 24.2834 30 25.9945 29.1112C27.4365 28.3621 28.6121 27.1865 29.3612 25.7445C30.25 24.0334 30.25 21.7855 30.25 17.2895V12.7105C30.25 8.21455 30.25 5.96656 29.3612 4.25547C28.6121 2.81355 27.4365 1.63786 25.9945 0.888841C24.2834 0 22.0355 0 17.5395 0H12.9605C8.46455 0 6.21656 0 4.50547 0.888841C3.06355 1.63786 1.88786 2.81355 1.13884 4.25547ZM2.94066 11.9737C3.05141 11.9737 3.14633 12.0001 3.23599 12.0528C3.32564 12.1055 3.3942 12.1741 3.44694 12.2637C3.49968 12.3534 3.52605 12.4483 3.52605 12.5538C3.52605 12.6593 3.49968 12.7542 3.44694 12.8439C3.3942 12.9335 3.32564 13.0021 3.23599 13.0548C3.15161 13.1076 3.05141 13.1339 2.94066 13.1339C2.82991 13.1339 2.73498 13.1076 2.64532 13.0548C2.55567 13.0021 2.48711 12.9335 2.43437 12.8439C2.38163 12.7542 2.35526 12.6593 2.35526 12.5538C2.35526 12.4483 2.38163 12.3534 2.43437 12.2637C2.48711 12.1741 2.55567 12.1055 2.64532 12.0528C2.7297 12.0001 2.82991 11.9737 2.94066 11.9737ZM2.38164 13.6929H3.51023V17.8329H2.38164V13.6929ZM8.01406 12.7859C7.84002 12.6171 7.63962 12.4853 7.4023 12.3956C7.16498 12.3059 6.90656 12.2585 6.62177 12.2585H4.35404V17.8329H5.51955V16.0767H6.56376C6.8591 16.0767 7.12806 16.0293 7.37066 15.9396C7.61325 15.8499 7.8242 15.7181 7.99824 15.5493C8.17227 15.3806 8.30939 15.1802 8.4096 14.9481C8.50452 14.7161 8.55199 14.4524 8.55199 14.1676C8.55199 13.8828 8.50452 13.6191 8.41487 13.3871C8.31994 13.155 8.1881 12.9546 8.01406 12.7859ZM7.08587 14.9112C6.90129 15.08 6.64287 15.1696 6.30535 15.1696H5.51955V13.1867H6.31062C6.64287 13.1867 6.90656 13.271 7.09114 13.4451C7.27573 13.6138 7.36538 13.8617 7.36538 14.1781C7.36538 14.4946 7.27045 14.7372 7.08587 14.9112ZM10.7195 13.6033C11.0782 13.6033 11.384 13.6613 11.6477 13.7773C11.9114 13.8934 12.1171 14.0516 12.2542 14.2625C12.3966 14.4682 12.4705 14.7108 12.4705 14.9903V17.8382H11.3788V17.2053H11.3577C11.2786 17.3477 11.1731 17.4743 11.0413 17.5797C10.9094 17.6799 10.7617 17.7643 10.5983 17.8171C10.4348 17.8751 10.266 17.9014 10.092 17.9014C9.83355 17.9014 9.59623 17.8487 9.38528 17.7432C9.17432 17.6378 9.01083 17.4901 8.88954 17.3002C8.76824 17.1104 8.71023 16.8941 8.71023 16.6463C8.71023 16.2666 8.84735 15.9765 9.12686 15.7603C9.40637 15.5441 9.80191 15.4228 10.3187 15.3911L11.363 15.3278V15.0536C11.363 14.8637 11.2997 14.7108 11.1731 14.6053C11.0518 14.4998 10.883 14.4471 10.6668 14.4471C10.4559 14.4471 10.2871 14.4946 10.1605 14.5895C10.034 14.6844 9.95485 14.8057 9.92848 14.9587H8.89481C8.91063 14.6897 8.99501 14.4576 9.14268 14.252C9.29562 14.0516 9.5013 13.8934 9.77026 13.7773C10.0392 13.6613 10.3557 13.6033 10.7195 13.6033ZM11.2469 16.7148C11.326 16.5988 11.3682 16.467 11.3682 16.3193H11.3735V16.0187L10.498 16.0714C10.2871 16.0873 10.1236 16.14 10.0076 16.2296C9.89156 16.3193 9.83355 16.4353 9.83355 16.583C9.83355 16.7412 9.89156 16.8625 10.0076 16.9522C10.1289 17.0418 10.2818 17.0893 10.4717 17.0893C10.6352 17.0893 10.7828 17.0524 10.92 16.9891C11.0571 16.9205 11.1678 16.8309 11.2469 16.7148ZM16.0144 14.4049C15.9459 14.252 15.851 14.1149 15.7297 13.9988C15.6084 13.8828 15.4607 13.7932 15.2972 13.7246C15.1337 13.6613 14.9491 13.6244 14.7487 13.6244C14.4007 13.6244 14.1 13.7088 13.8469 13.8828C13.5938 14.0568 13.3986 14.2994 13.2615 14.6211C13.1244 14.9376 13.0558 15.3173 13.0558 15.755C13.0558 16.198 13.1244 16.5777 13.2615 16.8994C13.3986 17.2211 13.5938 17.4637 13.8469 17.6378C14.1 17.8118 14.3954 17.8962 14.7435 17.8962C14.9491 17.8962 15.1337 17.8645 15.3025 17.796C15.4712 17.7327 15.6189 17.6378 15.7455 17.5217C15.8721 17.4057 15.967 17.2686 16.0355 17.1157H16.0566V17.8276H17.1694V12.2637H16.0408V14.4049H16.0144ZM15.9248 16.4195C15.851 16.6041 15.7402 16.7465 15.6031 16.8467C15.466 16.9469 15.3025 16.9944 15.1179 16.9944C14.928 16.9944 14.7645 16.9469 14.6274 16.8467C14.4903 16.7465 14.3848 16.6041 14.311 16.4195C14.2372 16.2349 14.2003 16.0187 14.2003 15.7656C14.2003 15.5124 14.2372 15.2962 14.311 15.1116C14.3848 14.927 14.4903 14.7846 14.6274 14.6844C14.7645 14.5842 14.928 14.5315 15.1179 14.5315C15.3025 14.5315 15.466 14.5842 15.6031 14.6844C15.7402 14.7846 15.8457 14.927 15.9248 15.1116C15.9986 15.2962 16.0408 15.5124 16.0408 15.7656C16.0408 16.0187 16.0039 16.2349 15.9248 16.4195ZM21.6363 12.3639C21.9686 12.4958 22.2533 12.6856 22.4854 12.9388C22.7227 13.1919 22.902 13.4978 23.0233 13.8459C23.1499 14.1992 23.2132 14.6 23.2132 15.043C23.2132 15.486 23.1499 15.8868 23.0233 16.2402C22.8967 16.5935 22.7174 16.8941 22.4854 17.1473C22.2481 17.3952 21.9686 17.5903 21.6363 17.7221C21.3093 17.854 20.9349 17.9225 20.5235 17.9225C20.1069 17.9225 19.7377 17.854 19.4055 17.7221C19.0785 17.5903 18.7937 17.4004 18.5617 17.1473C18.3296 16.8994 18.1503 16.5935 18.0237 16.2402C17.8972 15.8868 17.8339 15.486 17.8339 15.043C17.8339 14.6 17.8972 14.1992 18.0237 13.8459C18.1503 13.4925 18.3296 13.1919 18.5617 12.9388C18.7937 12.6909 19.0732 12.4958 19.4055 12.3639C19.7377 12.2321 20.1069 12.1635 20.5235 12.1635C20.9349 12.1635 21.3041 12.2321 21.6363 12.3639ZM21.3146 16.7201C21.5414 16.5672 21.7154 16.3457 21.8367 16.0609C21.958 15.7761 22.0213 15.4386 22.016 15.0483C22.016 14.6633 21.9527 14.3205 21.8314 14.0357C21.7049 13.751 21.5308 13.5295 21.3093 13.3712C21.0878 13.2183 20.8241 13.1392 20.5183 13.1392C20.2177 13.1392 19.9487 13.213 19.7272 13.3712C19.5004 13.5295 19.3264 13.751 19.2051 14.0357C19.0838 14.3205 19.0205 14.658 19.0205 15.0483C19.0205 15.4386 19.0838 15.7761 19.2051 16.0609C19.3317 16.3457 19.5057 16.5619 19.7272 16.7201C19.954 16.873 20.2177 16.9522 20.5235 16.9522C20.8241 16.9522 21.0931 16.8783 21.3146 16.7201ZM27.8436 15.1432C27.5746 14.8954 27.1474 14.7108 26.5568 14.5948L25.9661 14.4735C25.6339 14.4049 25.4018 14.3205 25.2541 14.2098C25.1118 14.099 25.0379 13.9619 25.0379 13.7879C25.0379 13.6508 25.0801 13.5347 25.1592 13.4293C25.2383 13.329 25.3491 13.2499 25.4967 13.1919C25.6391 13.1339 25.8079 13.1075 26.003 13.1075C26.1982 13.1075 26.3722 13.1392 26.5251 13.1972C26.6781 13.2552 26.7994 13.3449 26.889 13.4609C26.9787 13.5769 27.0314 13.7088 27.042 13.867H28.1442C28.1389 13.5242 28.044 13.2236 27.87 12.9704C27.6959 12.7173 27.4481 12.5169 27.1316 12.3745C26.8152 12.2321 26.4408 12.1635 26.003 12.1635C25.6919 12.1635 25.4071 12.2057 25.1487 12.2848C24.8903 12.3639 24.6635 12.48 24.4736 12.6329C24.2838 12.7858 24.1361 12.9652 24.0306 13.1708C23.9251 13.3765 23.8724 13.6086 23.8724 13.8617C23.8724 14.2783 24.0095 14.6159 24.2838 14.8795C24.558 15.1432 24.9746 15.3331 25.5284 15.4438L26.1138 15.5599C26.4618 15.6337 26.7097 15.7234 26.8574 15.8341C27.0051 15.9449 27.0736 16.0925 27.0736 16.2771C27.0736 16.4142 27.0314 16.5355 26.9418 16.641C26.8521 16.7465 26.7308 16.8309 26.5726 16.8941C26.4144 16.9574 26.2351 16.9891 26.0241 16.9891C25.8132 16.9891 25.6233 16.9574 25.4546 16.8994C25.2858 16.8414 25.1487 16.757 25.0432 16.641C24.9377 16.5303 24.8744 16.3931 24.8481 16.2402H23.7458C23.7564 16.5883 23.8566 16.8941 24.0464 17.142C24.2363 17.3952 24.4947 17.5903 24.827 17.7221C25.1592 17.8592 25.5495 17.9225 25.9925 17.9225C26.4566 17.9225 26.8574 17.8487 27.1949 17.7063C27.5272 17.5586 27.7856 17.353 27.9702 17.0893C28.1495 16.8203 28.2444 16.5092 28.2444 16.1453C28.2497 15.7286 28.1126 15.3911 27.8436 15.1432Z"
                fill="currentColor"
            />
        </svg>
    )
}

export default IpadOSIcon
