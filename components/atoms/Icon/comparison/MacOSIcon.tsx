const MacOSIcon = ({ ...props }) => {
    return (
        <svg
            width="30"
            height="30"
            viewBox="0 0 30 30"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            {...props}
        >
            <g clipPath="url(#clip0_13045_44488)">
                <path
                    d="M14.1912 18.2118C14.1912 20.7353 12.8765 22.3235 10.7765 22.3235C8.67647 22.3235 7.36176 20.7265 7.36176 18.2118C7.36176 15.6794 8.67647 14.0912 10.7765 14.0912C12.8765 14.0912 14.1912 15.6794 14.1912 18.2118ZM17.1206 9.30882L16.2029 9.37059C15.6824 9.40588 15.4529 9.59118 15.4529 9.92647C15.4529 10.2794 15.7441 10.4735 16.15 10.4735C16.7059 10.4735 17.1206 10.1118 17.1206 9.62647V9.30882ZM30.25 15C30.25 23.3735 23.6235 30 15.25 30C6.87647 30 0.25 23.3735 0.25 15C0.25 6.62647 6.87647 0 15.25 0C23.6235 0 30.25 6.62647 30.25 15ZM18.7265 9.12353C18.7265 10.3412 19.3971 11.0824 20.4912 11.0824C21.4176 11.0824 22 10.5618 22.0971 9.82059H21.3735C21.2765 10.2265 20.9588 10.4471 20.4912 10.4471C19.8735 10.4471 19.4941 9.94412 19.4941 9.12353C19.4941 8.31176 19.8735 7.82647 20.4912 7.82647C20.9853 7.82647 21.2941 8.10882 21.3735 8.47059H22.0971C22 7.74706 21.4353 7.2 20.4912 7.2C19.3971 7.19118 18.7265 7.93235 18.7265 9.12353ZM8.64118 7.27059V11.0118H9.38235V8.71765C9.38235 8.23235 9.72647 7.84412 10.1765 7.84412C10.6176 7.84412 10.9 8.10882 10.9 8.53235V11.0118H11.6235V8.64706C11.6235 8.19706 11.9324 7.84412 12.4176 7.84412C12.9029 7.84412 13.1412 8.09118 13.1412 8.61176V11.0118H13.8824V8.42647C13.8824 7.65 13.4412 7.19118 12.6824 7.19118C12.1618 7.19118 11.7294 7.45588 11.5441 7.86176H11.4824C11.3147 7.45588 10.9618 7.19118 10.45 7.19118C9.94706 7.19118 9.56765 7.43824 9.40882 7.86176H9.35588V7.26176L8.64118 7.27059ZM15.5059 18.2118C15.5059 14.9559 13.6882 12.9088 10.7765 12.9088C7.86471 12.9088 6.04706 14.9559 6.04706 18.2118C6.04706 21.4676 7.86471 23.5059 10.7765 23.5059C13.6882 23.5059 15.5059 21.4588 15.5059 18.2118ZM15.9647 11.0735C16.4588 11.0735 16.8647 10.8618 17.0941 10.4824H17.1559V11.0118H17.8706V8.45294C17.8706 7.66765 17.3412 7.2 16.3971 7.2C15.5412 7.2 14.9412 7.61471 14.8618 8.24118H15.5765C15.6559 7.96765 15.9471 7.81765 16.3618 7.81765C16.8647 7.81765 17.1294 8.04706 17.1294 8.46177V8.78824L16.1147 8.85C15.2235 8.90294 14.7206 9.29118 14.7206 9.97059C14.7118 10.6412 15.2412 11.0735 15.9647 11.0735ZM24.0559 20.4176C24.0559 18.9618 23.2088 18.1147 21.0735 17.6471L19.9353 17.4C18.5324 17.0912 17.9853 16.5353 17.9853 15.7324C17.9853 14.6912 18.9735 14.0647 20.2529 14.0647C21.5941 14.0647 22.4765 14.7529 22.5912 15.8824H23.8706C23.8088 14.1441 22.3353 12.9176 20.2882 12.9176C18.1618 12.9176 16.6706 14.1088 16.6706 15.7941C16.6706 17.25 17.5618 18.1853 19.6176 18.6353L20.7559 18.8824C22.1765 19.1912 22.75 19.7647 22.75 20.6206C22.75 21.6265 21.7353 22.3588 20.35 22.3588C18.8765 22.3588 17.8529 21.6882 17.7029 20.5853H16.4235C16.5471 22.3588 18.0382 23.5059 20.2794 23.5059C22.5647 23.5059 24.0559 22.3147 24.0559 20.4176Z"
                    fill="currentColor"
                />
            </g>
            <defs>
                <clipPath id="clip0_13045_44488">
                    <rect
                        width="30"
                        height="30"
                        fill="white"
                        transform="translate(0.25)"
                    />
                </clipPath>
            </defs>
        </svg>
    )
}

export default MacOSIcon
