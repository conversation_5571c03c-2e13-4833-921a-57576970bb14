const MeldIcon = ({ ...props }) => {
    return (
        <svg
            width="30"
            height="30"
            viewBox="0 0 30 30"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            {...props}
        >
            <g clipPath="url(#clip0_13045_44457)">
                <path
                    d="M21.1526 10.035C21.1693 10.0398 21.1851 10.0491 21.1982 10.0604C21.2111 10.0716 21.2219 10.0852 21.2291 10.1007C21.2363 10.1164 21.2398 10.1343 21.2398 10.1516V15.043C21.2399 15.0546 21.2376 15.0667 21.2344 15.0779C21.1223 15.4627 21.0668 15.8676 21.0668 16.289C21.0658 16.9913 21.2401 17.6828 21.5738 18.3008L21.5751 18.3021C21.8196 18.7533 22.1301 19.1444 22.5046 19.4703C22.574 19.5304 22.5582 19.6437 22.4724 19.6755C21.9506 19.871 21.3826 19.9692 20.769 19.9692C19.9605 19.9692 19.3328 19.7168 18.886 19.2128C18.8291 19.146 18.7757 19.0763 18.7264 19.0036C18.7174 18.9903 18.7119 18.9751 18.709 18.9593L18.3549 17.0655C18.3519 17.0494 18.3526 17.0319 18.3562 17.0159C18.3599 17.0001 18.3668 16.9848 18.3763 16.9717C18.386 16.9585 18.3986 16.9478 18.4126 16.9395C18.4266 16.9312 18.442 16.9255 18.4582 16.9234L18.8726 16.8697C18.8965 16.8665 18.9189 16.8562 18.937 16.8402C18.9551 16.824 18.9686 16.8021 18.9745 16.7785L19.0241 16.5773C19.0567 16.4469 19.0657 16.2775 19.0657 16.1026C19.0657 15.3926 18.931 14.7396 18.654 14.1524C18.5467 13.9203 18.4129 13.7009 18.2556 13.4993C18.2387 13.4779 18.2289 13.451 18.2288 13.4242V10.558C18.2289 10.5292 18.2395 10.5007 18.2583 10.4789C18.2772 10.4571 18.3036 10.443 18.3321 10.4387L21.1017 10.0323C21.1187 10.0298 21.1361 10.0302 21.1526 10.035Z"
                    fill="currentColor"
                />
                <path
                    fillRule="evenodd"
                    clipRule="evenodd"
                    d="M30.1628 10.035C30.1795 10.0398 30.1953 10.0491 30.2084 10.0604C30.2213 10.0717 30.2321 10.0852 30.2393 10.1007C30.2465 10.1164 30.25 10.1343 30.25 10.1516V19.7345C30.25 19.7665 30.2378 19.7977 30.2151 19.8204C30.1925 19.843 30.1613 19.8552 30.1293 19.8552H27.3892C27.3224 19.8552 27.2685 19.8009 27.2685 19.7345V19.0492C27.2682 18.9214 27.0763 18.8717 27.0016 18.9754C26.8267 19.2215 26.6076 19.4335 26.3552 19.5991C25.9747 19.8462 25.4985 19.9692 24.9281 19.9692C24.2721 19.9691 23.6781 19.8123 23.1457 19.4985C22.613 19.1753 22.1896 18.7332 21.8755 18.172C21.5714 17.6111 21.4196 16.9688 21.4195 16.2461C21.4195 15.5229 21.5714 14.8905 21.8755 14.3483C22.177 13.8186 22.616 13.3796 23.1457 13.0781C23.6781 12.7647 24.2722 12.6075 24.9281 12.6074C25.3941 12.6074 25.8411 12.7368 26.2693 12.9937C26.5359 13.1455 26.7643 13.3562 26.9359 13.6106C27.0122 13.7214 27.24 13.671 27.2403 13.5368V10.558C27.2404 10.5293 27.2498 10.5007 27.2685 10.4789C27.2873 10.4571 27.3139 10.443 27.3423 10.4387L30.1119 10.0323C30.1289 10.0297 30.1463 10.0302 30.1628 10.035ZM25.8562 14.8619C25.4473 14.8619 25.1047 14.9998 24.8289 15.275C24.5534 15.5509 24.4144 15.8898 24.4144 16.289C24.4146 16.6883 24.5535 17.0258 24.8289 17.3016C25.1047 17.5773 25.4667 17.7147 25.8562 17.7147C26.042 17.7176 26.2264 17.6825 26.3981 17.6114C26.5696 17.5403 26.7248 17.4349 26.8541 17.3016C27.13 17.0161 27.2685 16.669 27.2685 16.2595C27.2684 15.8509 27.1299 15.518 26.8541 15.2616C26.5783 14.9954 26.265 14.862 25.8562 14.8619Z"
                    fill="currentColor"
                />
                <path
                    fillRule="evenodd"
                    clipRule="evenodd"
                    d="M15.427 12.5698C16.1307 12.5698 16.7297 12.7265 17.2242 13.0406C17.7188 13.3346 18.1119 13.7726 18.3509 14.296C18.5984 14.8189 18.7223 15.408 18.7224 16.0637C18.7236 16.1606 18.7181 16.2583 18.7076 16.3547C18.704 16.3783 18.6928 16.3999 18.6754 16.4164C18.6581 16.4328 18.6361 16.4434 18.6124 16.4459L14.5606 16.973C14.4847 16.9829 14.4361 17.0599 14.4667 17.1299C14.5819 17.3925 14.7497 17.5887 14.9697 17.7187C15.226 17.8614 15.5639 17.9332 15.9823 17.9333C16.5886 17.9333 17.2419 17.7873 17.9405 17.4947C17.957 17.4877 17.9748 17.4846 17.9928 17.4853C18.0108 17.4861 18.0291 17.4903 18.0451 17.4987C18.0612 17.5073 18.0758 17.5203 18.0866 17.535C18.0973 17.5496 18.1047 17.5668 18.1081 17.5846L18.4193 19.2517C18.4243 19.2782 18.4197 19.3056 18.4072 19.3295C18.3945 19.3534 18.3744 19.3737 18.3495 19.3845C17.4884 19.749 16.5472 19.9303 15.5263 19.9303C14.7372 19.9303 14.0334 19.7736 13.4152 19.4596C13.0966 19.2924 12.8024 19.0818 12.5421 18.8332C12.5191 18.8114 12.5059 18.7796 12.5059 18.7474V16.3212C12.5059 15.2847 12.3558 14.6499 12.1599 14.1967C12.1511 14.1772 12.1482 14.1549 12.1505 14.1337C12.1528 14.1125 12.1607 14.0918 12.1733 14.0747C12.5044 13.6321 12.9342 13.2729 13.4286 13.0258C14.0278 12.7217 14.723 12.5698 15.427 12.5698ZM15.427 14.296C15.0843 14.296 14.8125 14.4241 14.6129 14.6809C14.4389 14.9047 14.3369 15.1865 14.3071 15.5258C14.3058 15.5439 14.3095 15.5628 14.3165 15.5795C14.3235 15.5959 14.3338 15.6108 14.3474 15.6224C14.3612 15.6341 14.378 15.6423 14.3957 15.6465C14.4133 15.6507 14.4317 15.6508 14.4493 15.6465L16.3887 15.2214C16.4044 15.2182 16.4198 15.2119 16.4329 15.2026C16.4459 15.1934 16.4569 15.1814 16.4651 15.1677C16.4733 15.154 16.4779 15.138 16.4799 15.1221C16.4818 15.1064 16.4804 15.0904 16.4759 15.0752C16.3073 14.5557 15.9579 14.2961 15.427 14.296Z"
                    fill="currentColor"
                />
                <path
                    d="M8.64726 12.5658C9.6185 12.5658 10.4452 12.8763 11.1258 13.4953C11.8063 14.1075 12.1465 14.847 12.1465 15.7136V19.752C12.1465 19.8402 12.0747 19.9116 11.9869 19.9116H9.23203C9.1439 19.9115 9.07251 19.8414 9.07242 19.7533V16.1991C9.07251 15.9986 9.11178 15.2417 8.41389 15.2415C7.7167 15.2415 7.75526 16.0378 7.75536 16.1991V19.752C7.75536 19.8401 7.68387 19.9114 7.59576 19.9116H4.92006C4.83208 19.9112 4.76046 19.84 4.76046 19.752V16.1991C4.76055 16.0182 4.79764 15.2415 4.10193 15.2415C3.40556 15.2416 3.45905 16.0292 3.44474 16.1871C3.44434 16.1952 3.44342 16.2037 3.4434 16.2125V19.752C3.4434 19.8402 3.37206 19.9116 3.2838 19.9116H0.409603C0.321345 19.9116 0.25 19.8402 0.25 19.752V12.8864C0.25 12.844 0.26697 12.8023 0.296942 12.7724C0.326878 12.7426 0.367392 12.7254 0.409603 12.7254H2.36508C2.38602 12.7254 2.40742 12.7295 2.42677 12.7375C2.44617 12.7455 2.46428 12.7574 2.47908 12.7724C2.49378 12.7872 2.50472 12.8053 2.51261 12.8247C2.52051 12.8441 2.52489 12.8654 2.52468 12.8864C2.51872 13.5446 2.52917 12.5658 4.32189 12.5658C5.51616 12.5658 5.86712 12.918 6.17006 13.2766C6.1837 13.293 6.20067 13.306 6.21969 13.3155C6.23885 13.3251 6.26001 13.3306 6.28138 13.3316C6.30274 13.3326 6.32445 13.3299 6.34442 13.3222C6.3644 13.3145 6.38286 13.3025 6.39807 13.2874C6.76179 12.9233 7.12227 12.5548 8.64726 12.5658Z"
                    fill="currentColor"
                />
            </g>
            <defs>
                <clipPath id="clip0_13045_44457">
                    <rect
                        width="30"
                        height="30"
                        fill="white"
                        transform="translate(0.25)"
                    />
                </clipPath>
            </defs>
        </svg>
    )
}

export default MeldIcon
